.entry-content img {
	margin: 0 0 1.5em 0;
}

.gallery-caption, .bypostauthor {
	font-weight: normal;
}

div.alignleft, img.alignleft {
	display: inline-block;
	float: left;
	margin-right: 1em;
	margin-bottom: 1em;
}
div.alignright, img.alignright {
	display: inline-block;
	float: right;
	margin-left: 1em;
	margin-top: 1em;
}
div.aligncenter, img.aligncenter {
	clear: both;
	display: block;
	margin-left: auto;
	margin-right: auto;
}
div.wp-caption {
    text-align: center;
    margin-top: 2em;
    margin-bottom: 2em;
    font-size: 12px;
}
.wp-caption img {
	border: 0 none;
	margin: 0;
	padding: 0;
	border-radius: 3px;
}
.wp-caption p.wp-caption-text {
	margin: 0;
	text-align: center;
	padding-bottom: 0 !important;
	font-size: 12px;
	color: #777;
}
.wp-smiley { 
	max-height: 1em;
	margin:0 !important;
}
.gallery dl {
	margin: 0;
	border: 0;
	padding: 0;
}

.wp-caption img, .alignnone img, .alignleft img, .alignright img {
    border: 0 none;
    margin: 0;
    padding: 0;
}

blockquote.left {
	float: left;
	margin-left: 0;
	margin-right: 20px;
	text-align: right;
	width: 33%;
}
blockquote.right {
	float: right;
	margin-left: 20px;
	margin-right: 0;
	text-align: left;
	width: 33%;
}

blockquote:before, blockquote:after, q:before, q:after 
{
	content:"";
}
blockquote, q 
{
	quotes:"" "";
}
a img 
{
	border:none;
}

p,
#page_content_wrapper .wpcf7 p,
.page_content_wrapper .wpcf7 p
{
	padding-top: 1em;
    padding-bottom: 1em;
}

p:empty
{
	padding: 0 !important;
}

em
{
	font-style: italic;
}

.size-auto, 
.size-full,
.size-large,
.size-medium,
.size-thumbnail {
	max-width: 100%;
	height: auto;
}

.center
{
	text-align: center;
}

#page_content_wrapper p, .page_content_wrapper p 
{
	padding-top: 1.2em;
	padding-bottom: 1.2em;
}

#page_content_wrapper p.woocommerce-info,
#page_content_wrapper p.woocommerce-message,
#page_content_wrapper p.woocommerce-error
{
	padding-top: 1em;
}

#page_content_wrapper p:empty, .page_content_wrapper p:empty
{
	padding: 0 !important;
	margin: 0 !important;
}

#footer p {
	padding-top:0.5em;
	padding-bottom:0.5em;
}

img, a img {
	image-rendering: optimizeQuality;
}

em { font-style: italic; }

::selection {
	background: #efc337;
	color: #fff;
}

table
{
	border-spacing: 0;
}

.sticky .post_wrapper
{
	background: #FFFAE4;
	border-color: #FFFAE4 !important;
}

.marginright
{
	margin-right: 7px;
}

img.alignright 
{
	float:right; margin:0 0 1em 2em;
}

img.alignleft
{
	float:left; margin:0 2em 1em 0;
}

img.aligncenter 
{
	display: block; margin-left: auto; margin-right: auto
}

a img.alignright 
{
	float:right; margin:0 0 1em 2em;
}

a img.alignleft 
{
	float:left; margin:0 2em 1em 0;
}

a img.aligncenter 
{
	display: block; margin-left: auto; margin-right: auto
}

.screen-reader-text 
{
	clip: rect(1px, 1px, 1px, 1px);
	position: absolute !important;
	height: 1px;
	width: 1px;
	overflow: hidden;
}

.screen-reader-text:focus 
{
	background-color: #f1f1f1;
	border-radius: 3px;
	box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
	clip: auto !important;
	color: #21759b;
	display: block;
	font-size: 14px;
	font-size: 0.875rem;
	font-weight: bold;
	height: auto;
	left: 5px;
	line-height: normal;
	padding: 15px 23px 14px;
	text-decoration: none;
	top: 5px;
	width: auto;
	z-index: 100000; /* Above WP toolbar. */
}

body.page-template-default:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ul:not(.children),
body.page-template-default:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ol,
body.page-template-default:not(.elementor-page) #page_content_wrapper .inner .sidebar_content dl,
body.page-template-page-l:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ul:not(.children),
body.page-template-page-l:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ol,
body.page-template-page-l:not(.elementor-page) #page_content_wrapper .inner .sidebar_content dl,
body.page-template-page-r:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ul:not(.children),
body.page-template-page-r:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ol,
body.page-template-page-r:not(.elementor-page) #page_content_wrapper .inner .sidebar_content dl,
body.post-template-default:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ul:not(.children),
body.post-template-default:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ol,
body.post-template-default:not(.elementor-page) #page_content_wrapper .inner .sidebar_content dl
{
	margin: 5px 0 5px 20px;
}

body.post-template-default:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ul li ul:not(.children),
body.post-template-default:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ol li ol,
body.post-template-default:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ol li dl,
body.post-template-page-l:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ul li ul:not(.children),
body.post-template-page-l:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ol li ol,
body.post-template-page-l:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ol li dl,
body.post-template-page-r:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ul li ul:not(.children),
body.post-template-page-r:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ol li ol,
body.post-template-page-r:not(.elementor-page) #page_content_wrapper .inner .sidebar_content ol li dl
{
	margin: 5px 0 5px 20px;
}

body.archive #page_content_wrapper,
body.home.blog #page_content_wrapper
{
	padding-bottom: 60px;
}

body.page-template-default.elementor-page #page_content_wrapper
{
	padding-bottom: 0;
}

body.archive #page_content_wrapper,
body.home #page_content_wrapper
{
	padding-bottom: 60px;
}

body.error404 #page_content_wrapper
{
	padding-bottom: 20px;
}

body.home.blog #page_caption
{
	display: none;
}

.sidebar_widget li.widget_recent_comments ul li.recentcomments .comment-author-link
{
	font-style: italic;
}

.sidebar_widget li.widget_recent_comments ul li.recentcomments .comment-author-link a
{
	font-weight: 400;
}

.sidebar_widget li.widget_recent_comments ul li.recentcomments a
{
	font-weight: 900;
}

body.post-template-default:not(.elementor-page) #page_content_wrapper .inner .sidebar_content dl.gallery-item
{
	margin: 0 0 30px 0;
}

body.post-template-default:not(.elementor-page) #page_content_wrapper .inner .sidebar_content dl.gallery-item img
{
	border: 0;
}

/*
*
* Gutenberg CSS
*
*/

.wp-block-embed
{
	margin: 1em 0 1em 0;
}

.wp-block-embed .video-container,
.video-container
{
	position: relative;
	padding-bottom: 56.25%;
	padding-top: 25px;
	height: 0;
}

.wp-block-embed .video-container
{
	margin: 0;
}

.video-container
{
	padding-top: 0;
	margin: 0 0 1em 0;
}

.wp-block-embed .video-container iframe,
.video-container iframe
{
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}