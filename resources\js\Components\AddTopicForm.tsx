import React, {useState} from 'react';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/Components/ui/select";
import {Input} from "@/Components/ui/input";
import PrimaryButton from "@/Components/PrimaryButton";
import {useFormik} from "formik";
import * as Yup from "yup";
import axios from "axios";
import {CategoryType, SpeakerType} from "@/types/speaker-type";
import {FaqType} from "@/types/faq-type";
import {Button} from "@/Components/ui/button";
import {Textarea} from "@/Components/ui/textarea";
import {Label} from "@/Components/ui/label";
import {useDropzone} from "react-dropzone";

interface AddTopicFormProps {
  topicAdded? : (tag: CategoryType) => void;
  open? : boolean;
}

function AddTopicForm({ topicAdded} : AddTopicFormProps) {

  const formik = useFormik({
    initialValues: {
      name: '',
      image: '',
    },

    validationSchema: Yup.object({
      name: Yup.string().required('Topic name is required'),
    }),

    onSubmit: values => {

      //validate image
      if (!values.image) {
        formik.setErrors({image: 'Image is required'});
        return;
      }

      axios.post(route('admin.topics.store'), values, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }}
      ).then((response) => {
        formik.setSubmitting(false);
        if (topicAdded) {
          topicAdded(response.data);
        }
        formik.resetForm();
      }).catch((error) => {
        //Set formik errors
        if (error.response.status === 422) {
          formik.setErrors(error.response.data.errors);
        }
        formik.setSubmitting(false);
      });
    },

  });

  const [ imagePreview, setImagePreview ] = useState<string | null>(  null );

  const {
    acceptedFiles,
    getRootProps: getRootProps,
    getInputProps: getInputProps,
  } = useDropzone( {
    maxFiles: 1,
    accept: {
      'image/*': [],
    },
    onDrop: ( acceptedFiles ) => {
      setImagePreview( URL.createObjectURL( acceptedFiles[ 0 ] ) );
      formik.setFieldValue( 'image', acceptedFiles[ 0 ] );
    },
  } );

  return (
    <form onSubmit={formik.handleSubmit}
          className=" max-w-4xl space-y-8 mx-auto py-8 px-8">
      <div>
        <label htmlFor="link" className="block text-sm font-medium text-gray-700">Topic name</label>
        <div className="mt-1">
          <Input type="text"
                 name="name"
                 value={formik.values.name}
                 onChange={formik.handleChange}
                 id="link"
                 placeholder={'Topic'}/>
        </div>
        {
          formik.touched.name && formik.errors.name ? (
            <div className="text-red-500 text-xs italic">{formik.errors.name}</div>
          ) : null
        }
      </div>


      <div>
        <div className='w-full'>
          <Label htmlFor={'file'}>Image</Label>
          <div {...getRootProps({className: 'border-dashed border-2 rounded-lg mt-2 py-4 px-4'})}>
            <input {...getInputProps()} />
            <p className={'text-sm'}>Drag 'n' Cover Image, or click to select files</p>
          </div>

          {/*    display preview */}
          {imagePreview && (
            <div className='mx-auto mt-3 w-full flex items-center justify-center'>
              <img src={imagePreview} className={'h-40 w-60 rounded-lg object-cover'} alt=''/>
            </div>
          )}

        </div>
      </div>

      <div>
        <div className="mt-1 flex justify-end">
          <Button disabled={formik.isSubmitting} type="submit">
            Add Topic
          </Button>
        </div>
      </div>
    </form>
  );
}

export default AddTopicForm;
