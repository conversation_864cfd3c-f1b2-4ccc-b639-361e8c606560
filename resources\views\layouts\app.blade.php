<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'MENA Speakers') }}</title>
        <!-- Fonts -->
        <link rel="stylesheet" href="https://fonts.bunny.net/css2?family=Nunito:wght@400;600;700&display=swap">

        <!-- Scripts -->
    @stack('styles')
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-100">
            @include('layouts.navigation')

            <!-- Page Heading -->
            @if (isset($header))
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endif

            <!-- Page Content -->
            <main>
                {{ $slot }}
            </main>
        </div>
    @stack('body-script')
        <!-- Start of HubSpot Embed Code -->
        <script type="text/javascript" id="hs-script-loader" async defer src="//js.hs-scripts.com/5847879.js"></script>
        <!-- End of HubSpot Embed Code -->

{{--        <script charset="utf-8" type="text/javascript" src="//js.hsforms.net/forms/embed/v2.js"></script>--}}

    </body>
</html>
@push('styles')
    @if(isset($speaker))
        <meta property="og:title" content="{{ $speaker->name }}" />
        <meta property="og:description" content="{{ $speaker->excerpt ?? Str::limit($speaker->bio, 150) }}" />
        <meta property="og:image" content="{{ $speaker->image }}" />
        <meta property="og:url" content="{{ url()->current() }}" />
        <meta property="og:type" content="profile" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="{{ $speaker->name }}" />
        <meta name="twitter:description" content="{{ $speaker->excerpt ?? Str::limit($speaker->bio, 150) }}" />
        <meta name="twitter:image" content="{{ $speaker->image }}" />
    @endif
@endpush
