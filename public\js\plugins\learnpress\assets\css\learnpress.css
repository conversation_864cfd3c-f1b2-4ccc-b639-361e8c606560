/**
 * Mixin
 */
@-webkit-keyframes rotating4 /* <PERSON><PERSON> and Chrome */ {
  from {
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg); }
  to {
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg); } }

@keyframes rotating4 {
  from {
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg); }
  to {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg); } }

@-webkit-keyframes animation4 /* Safari and Chrome */ {
  from {
    left: -40%;
    width: 40%; }
  to {
    left: 100%;
    width: 10%; } }

@keyframes animation4 {
  from {
    left: -40%;
    width: 40%; }
  to {
    left: 100%;
    width: 10%; } }

@keyframes lp-rotating {
  from {
    transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg); }
  to {
    transform: rotate(360deg);
    -o-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg); } }

@-webkit-keyframes lp-rotating {
  from {
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg); }
  to {
    transform: rotate(360deg);
    -webkit-transform: rotate(360deg); } }

.ajaxload {
  background: #7b7b7b;
  display: inline-block;
  width: 30px;
  height: 30px;
  position: relative;
  content: '';
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  -webkit-animation: lp-rotating 1s linear infinite;
  -moz-animation: lp-rotating 1s linear infinite;
  animation: lp-rotating 1s linear infinite; }
  .ajaxload:after {
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    display: inline-block;
    width: 6px;
    height: 6px;
    background: #DEDEDE;
    position: absolute;
    content: '';
    top: 2px;
    left: 50%;
    margin-left: -3px; }

.learn-press-checkout.guest-checkout {
  display: none; }

.button-continue-guest-checkout {
  clear: both; }

#learn-press-payment .payment-methods {
  list-style: none;
  margin: 0;
  padding: 0; }
  #learn-press-payment .payment-methods .lp-payment-method {
    margin-bottom: 20px; }
    #learn-press-payment .payment-methods .lp-payment-method > label {
      display: block;
      background: #F5F5F5;
      padding: 10px 20px;
      line-height: 40px; }
      #learn-press-payment .payment-methods .lp-payment-method > label img {
        vertical-align: middle; }
    #learn-press-payment .payment-methods .lp-payment-method.selected > label {
      background: #e7f7ff; }
  #learn-press-payment .payment-methods .payment-method-form {
    display: none;
    background: #F9f9f9;
    padding: 15px 20px;
    border-top: 1px solid #DDD; }

#learn-press-checkout-login,
#learn-press-checkout-register {
  margin-bottom: 1.5em;
  border: 1px solid #DDD;
  padding: 20px 20px 0 20px;
  background: #FFFFFF; }

#learn-press-order-review,
.learn-press-checkout-comment {
  margin-bottom: 20px; }

#checkout-form-login,
#checkout-form-register {
  _display: none; }
  #checkout-form-login .learn-press-form-register,
  #checkout-form-login .learn-press-form-login,
  #checkout-form-register .learn-press-form-register,
  #checkout-form-register .learn-press-form-login {
    display: none; }

#checkout-guest-email {
  border: 1px solid #DDD;
  padding: 20px;
  margin: 0 0 20px 0; }
  #checkout-guest-email .form-heading {
    margin: 0; }

#checkout-guest-email #checkout-guest-options {
  list-style: none;
  margin: 0; }

#checkout-guest-email #checkout-existing-account,
#checkout-guest-email #checkout-new-account {
  display: none;
  margin: 0; }

#checkout-guest-email.email-exists #checkout-existing-account {
  display: block; }

#checkout-guest-email.email-exists #checkout-new-account {
  display: none; }

.lp-list-table {
  border: none;
  width: 100%;
  margin-bottom: 20px; }
  .lp-list-table th, .lp-list-table td {
    border: 0 solid #DDD;
    border-bottom-width: 1px;
    line-height: 1;
    font-size: 16px;
    padding: 15px 15px;
    background: #FFF;
    text-align: left; }
  .lp-list-table tr {
    border: 0; }
  .lp-list-table thead tr {
    color: #444; }
    .lp-list-table thead tr th {
      background: #00adff;
      color: #FFF;
      border-bottom: none; }
  .lp-list-table tbody tr {
    color: #777; }
    .lp-list-table tbody tr td, .lp-list-table tbody tr th {
      border-bottom: 1px solid #DDD;
      font-size: 14px;
      background: #FFF; }
      .lp-list-table tbody tr td a, .lp-list-table tbody tr th a {
        text-decoration: none;
        border-bottom: none; }
    .lp-list-table tbody tr:nth-child(odd) {
      background: #F5F5F5; }
  .lp-list-table .list-table-nav td {
    font-size: 14px; }
    .lp-list-table .list-table-nav td.nav-text {
      text-align: left; }
    .lp-list-table .list-table-nav td.nav-pages {
      text-align: right; }
      .lp-list-table .list-table-nav td.nav-pages .learn-press-pagination {
        text-align: right; }
      .lp-list-table .list-table-nav td.nav-pages .page-numbers {
        margin-bottom: 0; }

.lp-label {
  font-size: 12px;
  display: inline-block;
  padding: 3px 5px;
  background: #DDD;
  border-radius: 4px;
  line-height: 1;
  color: #FFF; }
  .lp-label.label-finished, .lp-label.label-completed {
    background: #ffdc1e; }
  .lp-label.label-passed {
    background: #14c4ff; }
  .lp-label.label-failed {
    background: #ff4d66; }
  .lp-label.label-enrolled, .lp-label.label-started {
    background: #d243ff; }

.learn-press-form .form-fields {
  list-style: none;
  margin: 0;
  padding: 0; }
  .learn-press-form .form-fields .form-field {
    margin: 0 0 20px 0; }
    .learn-press-form .form-fields .form-field label {
      display: block;
      margin: 0 0 10px 0; }
    .learn-press-form .form-fields .form-field input[type="text"],
    .learn-press-form .form-fields .form-field input[type="email"],
    .learn-press-form .form-fields .form-field input[type="number"],
    .learn-press-form .form-fields .form-field input[type="password"],
    .learn-press-form .form-fields .form-field textarea {
      padding: 8px;
      width: 100%;
      -webkit-box-sizing: border-box;
      -moz-box-sizing: border-box;
      box-sizing: border-box; }
    .learn-press-form .form-fields .form-field .description {
      font-style: italic;
      margin-top: 10px;
      font-size: 14px;
      line-height: 1.4; }
    .learn-press-form .form-fields .form-field .asterisk {
      color: #FF0000; }

.form-desc {
  font-size: smaller;
  font-style: italic; }

.become-teacher-form {
  margin: 0 auto;
  width: 90%;
  max-width: 500px; }
  .become-teacher-form .form-field input[type="text"],
  .become-teacher-form .form-field input[type="email"],
  .become-teacher-form .form-field input[type="number"],
  .become-teacher-form .form-field input[type="password"] {
    width: 100%; }

#learn-press-user-profile {
  position: relative; }
  #learn-press-user-profile:after {
    clear: both;
    display: block;
    content: ''; }

#learn-press-profile-header {
  height: 100px;
  background: #f0defb;
  position: relative;
  z-index: 100; }
  #learn-press-profile-header .lp-profile-avatar {
    position: absolute;
    bottom: -10px;
    left: 10px; }
    #learn-press-profile-header .lp-profile-avatar img {
      border-radius: 0; }

#learn-press-profile-nav {
  float: left;
  min-width: 200px;
  padding-top: 40px;
  background: #f5f5f5;
  /*margin-bottom: -20000px;
    padding-bottom: 20000px;*/ }
  #learn-press-profile-nav:before {
    content: '';
    width: 200px;
    background: #F5F5F5;
    top: 0;
    bottom: 0;
    position: absolute; }
  #learn-press-profile-nav .tabs {
    list-style: none;
    margin: 0;
    padding: 0; }
    #learn-press-profile-nav .tabs > li {
      margin-bottom: 0;
      position: relative; }
      #learn-press-profile-nav .tabs > li ul {
        list-style: none;
        margin: 0;
        padding: 8px 0;
        display: none; }
        #learn-press-profile-nav .tabs > li ul li a {
          padding-left: 20px; }
        #learn-press-profile-nav .tabs > li ul li.active a {
          color: #FFF; }
        #learn-press-profile-nav .tabs > li ul li:hover a {
          background: transparent;
          color: #FFF; }
      #learn-press-profile-nav .tabs > li a {
        display: block;
        text-decoration: none;
        padding: 5px 10px;
        border: none;
        box-shadow: none;
        outline: none; }
        #learn-press-profile-nav .tabs > li a:hover {
          background: #FFF; }
      #learn-press-profile-nav .tabs > li.active ul {
        background: #EAEAEA;
        display: block; }
      #learn-press-profile-nav .tabs > li.active > a {
        background: #D9D9D9; }
      #learn-press-profile-nav .tabs > li:hover:not(.active) > a {
        background: #D0D0D0;
        color: #FFF; }
      #learn-press-profile-nav .tabs > li:hover:not(.active) ul {
        background: #EAEAEA;
        display: block;
        position: absolute;
        width: 200px;
        top: 0;
        margin: 0;
        padding: 0;
        left: 100%;
        z-index: 100; }

#learn-press-profile-content {
  float: right;
  width: calc(100% - 230px);
  margin-top: 30px;
  overflow: hidden; }
  #learn-press-profile-content .lp-sections {
    list-style: none;
    overflow: hidden; }
    #learn-press-profile-content .lp-sections li {
      float: left; }
      #learn-press-profile-content .lp-sections li a {
        text-decoration: none;
        display: block;
        padding: 3px 5px;
        border: none;
        box-shadow: none; }
      #learn-press-profile-content .lp-sections li.active a {
        border-bottom: 2px solid #DDD; }

#learn-press-profile-footer {
  clear: both;
  background: #FFF;
  z-index: 10;
  position: relative; }

.lp-user-profile .profile-name {
  display: block;
  position: absolute;
  width: 100%;
  text-align: center;
  font-size: 13px; }

.profile-list-table td.column-actions {
  white-space: nowrap; }

.profile-list-table .result-percent {
  font-size: 12px;
  font-weight: bold;
  min-width: 40px;
  display: inline-block; }

.profile-list-courses .column-passing-grade,
.profile-list-courses .column-time-interval,
.profile-list-courses .column-date,
.profile-list-courses .column-status,
.profile-list-quizzes .column-passing-grade,
.profile-list-quizzes .column-time-interval,
.profile-list-quizzes .column-date,
.profile-list-quizzes .column-status {
  width: 150px; }

.profile-recover-order {
  padding: 20px;
  border: 1px solid #DDD;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px; }
  .profile-recover-order p {
    margin-bottom: 10px; }
  .profile-recover-order input[name="order-key"] {
    width: 250px;
    display: inline-block; }

.order-recover input[name="order-key"], .order-recover button {
  vertical-align: middle; }

.column-order-action a {
  display: inline-block;
  margin-right: 5px; }

.lp-tab-sections {
  list-style: none;
  margin: 0 0 20px 0;
  padding: 0;
  background: #eaeaea; }
  .lp-tab-sections .section-tab {
    float: left;
    list-style: none; }
    .lp-tab-sections .section-tab a, .lp-tab-sections .section-tab span {
      display: inline-block;
      padding: 8px 10px;
      line-height: 1;
      box-shadow: none; }
    .lp-tab-sections .section-tab.active span {
      border-bottom: 2px solid #00adff;
      padding-bottom: 6px; }
  .lp-tab-sections:after {
    clear: both;
    display: block;
    content: ''; }

.profile-heading {
  font-size: 18px; }

.lp-sub-menu {
  list-style: none;
  margin: 0 0 10px 0; }
  .lp-sub-menu li {
    display: inline-block; }
    .lp-sub-menu li a, .lp-sub-menu li span {
      display: inline-block;
      margin: 0 10px 0 0; }

.learn-press-nav-items .learn-press-pagination {
  float: right; }

.learn-press-nav-items:after {
  clear: both;
  display: block;
  content: ''; }

.lp-avatar-preview {
  position: relative;
  float: left;
  background-color: #DDD;
  margin-bottom: 20px;
  overflow: hidden; }
  .lp-avatar-preview .profile-picture {
    float: left;
    margin-right: -100%;
    width: 100%;
    margin-top: -25%; }
    .lp-avatar-preview .profile-picture img {
      width: 100%;
      height: 100%;
      border-radius: 0; }
    .lp-avatar-preview .profile-picture.profile-avatar-hidden {
      display: none; }
  .lp-avatar-preview .lp-avatar-preview-actions {
    position: absolute;
    top: 50%;
    width: 100%;
    margin-top: -14px;
    text-align: center; }
    .lp-avatar-preview .lp-avatar-preview-actions a {
      text-decoration: none;
      font-size: 12px;
      background: #FFF;
      display: inline-block;
      box-shadow: none;
      padding: 5px 10px; }
  .lp-avatar-preview .lp-avatar-upload-progress {
    position: absolute;
    height: 10px;
    background: #FFF;
    top: 50%;
    margin-top: -5px;
    left: 10px;
    right: 10px;
    display: none; }
    .lp-avatar-preview .lp-avatar-upload-progress .lp-avatar-upload-progress-value {
      width: 0;
      height: 10px;
      background: #563d7c; }
  .lp-avatar-preview .lp-avatar-upload-error {
    display: none; }
  .lp-avatar-preview .lp-avatar-preview-actions {
    display: none; }
  .lp-avatar-preview:hover .lp-avatar-preview-actions {
    display: block; }
  .lp-avatar-preview.uploading .lp-avatar-preview-actions, .lp-avatar-preview.upload-error .lp-avatar-preview-actions {
    display: none; }
  .lp-avatar-preview.uploading .lp-avatar-upload-progress, .lp-avatar-preview.upload-error .lp-avatar-upload-progress {
    display: block; }
  .lp-avatar-preview.uploading:before, .lp-avatar-preview.upload-error:before {
    position: absolute;
    width: 100%;
    height: 100%;
    background: #FFF;
    opacity: 0.4;
    content: ''; }
  .lp-avatar-preview.upload-error .lp-avatar-upload-error {
    display: block; }
  .lp-avatar-preview.croping .lp-avatar-preview-actions {
    display: none; }
  .lp-avatar-preview.croping .lp-avatar-crop-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    overflow: hidden; }
    .lp-avatar-preview.croping .lp-avatar-crop-image img {
      max-width: inherit;
      cursor: move; }
    .lp-avatar-preview.croping .lp-avatar-crop-image .lp-crop-controls {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 30px; }
      .lp-avatar-preview.croping .lp-avatar-crop-image .lp-crop-controls .lp-zoom {
        position: absolute;
        height: 10px;
        bottom: 10px;
        left: 10px;
        right: 30px;
        background: #563d7a; }
        .lp-avatar-preview.croping .lp-avatar-crop-image .lp-crop-controls .lp-zoom .ui-slider {
          position: absolute;
          left: 0;
          right: 10px;
          height: 100%; }
          .lp-avatar-preview.croping .lp-avatar-crop-image .lp-crop-controls .lp-zoom .ui-slider .ui-slider-handle {
            display: inline-block;
            width: 10px;
            height: 14px;
            background: #FFF;
            position: absolute;
            margin-top: -2px;
            border: 1px solid #563d7a;
            box-shadow: none;
            outline: none;
            cursor: ew-resize; }
      .lp-avatar-preview.croping .lp-avatar-crop-image .lp-crop-controls .lp-cancel-upload {
        text-decoration: none;
        position: absolute;
        bottom: 5px;
        right: 5px;
        box-shadow: none; }
      .lp-avatar-preview.croping .lp-avatar-crop-image .lp-crop-controls:before {
        position: absolute;
        height: 30px;
        width: 100%;
        content: '';
        background: #FFF;
        opacity: 0.4; }
  .lp-avatar-preview:after {
    clear: both;
    display: block;
    content: ''; }

#profile-mobile-menu {
  display: none; }

@media screen and (max-width: 480px) {
  #learn-press-user-profile:before {
    display: none; }
  #learn-press-profile-nav {
    height: 40px;
    z-index: 90;
    /* position: relative; */
    padding-top: 0;
    min-width: 40px;
    width: 40px;
    float: right;
    margin-bottom: 20px; }
    #learn-press-profile-nav #profile-mobile-menu {
      display: block;
      width: 40px;
      height: 40px;
      float: right;
      line-height: 40px;
      text-align: center;
      cursor: pointer;
      box-shadow: none; }
    #learn-press-profile-nav .learn-press-tabs {
      right: 0;
      width: 100%;
      display: none;
      background: #03A9F4;
      color: #FFF;
      position: absolute;
      margin-top: 40px; }
      #learn-press-profile-nav .learn-press-tabs li a {
        color: #FFF; }
      #learn-press-profile-nav .learn-press-tabs li:hover:not(.active) > a {
        background: #D0D0D0;
        color: #FFF; }
      #learn-press-profile-nav .learn-press-tabs li:hover:not(.active) ul {
        position: relative;
        left: 0;
        width: 100%; }
    #learn-press-profile-nav:hover #profile-mobile-menu {
      background: #03a8f4;
      /* border: 1px solid #DDD; */
      color: #FFF; }
    #learn-press-profile-nav:hover .learn-press-tabs {
      display: block; }
    #learn-press-profile-nav:before {
      content: '';
      position: absolute;
      left: 0;
      right: 40px;
      height: 40px;
      background: #F5F5F5; }
    #learn-press-profile-nav .tabs > li.active ul, #learn-press-profile-nav .tabs > li.active a, #learn-press-profile-nav .tabs > li:hover ul, #learn-press-profile-nav .tabs > li:hover a {
      background: #0191d4; }
  #learn-press-profile-content {
    float: none;
    width: 100%; } }

#learn-press-user-profile .learn-press-form-login,
#learn-press-user-profile .learn-press-form-register {
  border: 1px solid #DDD;
  padding: 20px 20px 0 20px;
  margin-bottom: 20px; }

#learn-press-course-tabs .course-tab-panel {
  display: none; }
  #learn-press-course-tabs .course-tab-panel.active {
    display: block; }

.lp-single-course .course-author {
  margin-bottom: 40px; }
  .lp-single-course .course-author .author-name {
    float: left;
    margin: 0 20px 0 0;
    text-align: center; }
    .lp-single-course .course-author .author-name img {
      width: 80px;
      height: 80px;
      border-radius: 50%; }
    .lp-single-course .course-author .author-name a {
      display: block; }
  .lp-single-course .course-author .author-bio {
    font-size: 15px;
    font-style: italic; }
  .lp-single-course .course-author:after {
    clear: both;
    display: block;
    content: ''; }
  .lp-single-course .course-author .course-instructor-item {
    clear: both;
    padding-top: 20px; }

.lp-single-course .course-price {
  margin-bottom: 10px; }
  .lp-single-course .course-price .origin-price,
  .lp-single-course .course-price .price {
    vertical-align: middle; }
  .lp-single-course .course-price .origin-price {
    font-size: 18px;
    text-decoration: line-through;
    font-style: italic;
    margin-right: 10px; }
  .lp-single-course .course-price .price {
    font-size: 24px; }

.lp-single-course .lp-course-buttons {
  margin-bottom: 20px; }

.edit-content {
  margin-left: 5px; }

ul.learn-press-nav-tabs {
  list-style: none;
  border-bottom: 3px solid #DDD;
  margin: 0 0 20px 0;
  padding: 0; }
  ul.learn-press-nav-tabs .course-nav {
    float: left;
    position: relative;
    margin: 0;
    list-style: none; }
    ul.learn-press-nav-tabs .course-nav a {
      display: inline-block;
      padding: 10px 20px;
      border-bottom: none;
      outline: none;
      -webkit-box-shadow: none;
      -moz-box-shadow: none;
      box-shadow: none; }
      ul.learn-press-nav-tabs .course-nav a:focus {
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none; }
    ul.learn-press-nav-tabs .course-nav.active:after, ul.learn-press-nav-tabs .course-nav:hover:after {
      position: absolute;
      bottom: -3px;
      background: #00adff;
      height: 3px;
      width: 100%;
      content: '';
      left: 0; }
  ul.learn-press-nav-tabs:after {
    clear: both;
    display: block;
    content: ''; }

.course-item-popup #tab-curriculum {
  display: block; }

.course-curriculum ul.curriculum-sections {
  list-style: none;
  margin: 0;
  padding: 0; }
  .course-curriculum ul.curriculum-sections .section {
    margin: 0;
    padding: 0; }
    .course-curriculum ul.curriculum-sections .section.section-empty .section-header {
      margin-bottom: 20px; }
    .course-curriculum ul.curriculum-sections .section.section-empty .learn-press-message {
      margin-left: 15px;
      margin-right: 15px; }
  .course-curriculum ul.curriculum-sections .section-header {
    display: table;
    border-bottom: 1px solid #00adff;
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box; }
    .course-curriculum ul.curriculum-sections .section-header .section-left {
      display: table-cell;
      vertical-align: top;
      cursor: pointer; }
    .course-curriculum ul.curriculum-sections .section-header .section-title,
    .course-curriculum ul.curriculum-sections .section-header .section-desc {
      margin: 0 0 10px 0; }
    .course-curriculum ul.curriculum-sections .section-header .section-title {
      font-weight: normal;
      margin-bottom: 0;
      font-size: 18px;
      padding: 10px 0; }
    .course-curriculum ul.curriculum-sections .section-header .section-desc {
      font-size: 14px;
      font-style: italic; }
    .course-curriculum ul.curriculum-sections .section-header .section-meta {
      display: table-cell;
      white-space: nowrap;
      padding-left: 20px;
      text-align: right;
      font-size: 14px;
      vertical-align: middle; }
      .course-curriculum ul.curriculum-sections .section-header .section-meta .section-progress {
        display: inline-block;
        margin-right: 5px; }
        .course-curriculum ul.curriculum-sections .section-header .section-meta .section-progress .progress-bg {
          width: 100px; }
  .course-curriculum ul.curriculum-sections .section-content {
    list-style: none;
    margin: 0 0 15px 0;
    padding: 0; }
    .course-curriculum ul.curriculum-sections .section-content .course-item {
      position: relative;
      font-size: 14px;
      border-bottom: 1px solid #EEE;
      transition: padding-left linear 0.15s;
      background: #FFF;
      margin: 0;
      padding: 0; }
      .course-curriculum ul.curriculum-sections .section-content .course-item .section-item-link {
        border-bottom: none;
        display: table;
        box-shadow: none;
        outline: none;
        width: 100%;
        line-height: 1.5; }
        .course-curriculum ul.curriculum-sections .section-content .course-item .section-item-link:before {
          font-family: fontawesome;
          font-size: 18px;
          /*position: absolute;*/
          left: 0;
          transition: left linear 0.15s;
          display: table-cell;
          width: 20px;
          padding: 10px 0; }
      .course-curriculum ul.curriculum-sections .section-content .course-item .item-icon,
      .course-curriculum ul.curriculum-sections .section-content .course-item .item-name {
        display: table-cell;
        vertical-align: middle;
        padding: 10px 10px; }
      .course-curriculum ul.curriculum-sections .section-content .course-item .item-icon:before {
        font-size: 18px; }
      .course-curriculum ul.curriculum-sections .section-content .course-item .item-icon.icon-lock {
        float: right;
        margin-top: 15px; }
      .course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta {
        display: table-cell;
        vertical-align: middle;
        white-space: nowrap;
        padding: 10px 0;
        text-align: right; }
        .course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta .item-meta {
          height: 20px;
          line-height: 20px;
          text-align: center;
          display: inline-block;
          vertical-align: middle;
          margin-left: 5px;
          padding: 0 8px;
          border-radius: 3px;
          font-size: 14px;
          color: #FFF; }
          .course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta .item-meta.final-quiz {
            background: #14c4ff; }
          .course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta .item-meta.trans {
            padding: 0; }
        .course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta .count-questions {
          background: #dd88ae; }
        .course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta .duration {
          background: #009688; }
        .course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta .course-item-status {
          color: #DDD;
          margin-left: 5px;
          display: none;
          -webkit-border-radius: 50%;
          -moz-border-radius: 50%;
          border-radius: 50%;
          -webkit-box-sizing: border-box;
          -moz-box-sizing: border-box;
          box-sizing: border-box; }
          .course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta .course-item-status:before {
            vertical-align: middle;
            font-size: 14px; }
      .course-curriculum ul.curriculum-sections .section-content .course-item.has-status .course-item-status {
        display: inline-block; }
      .course-curriculum ul.curriculum-sections .section-content .course-item.has-status.status-viewed .course-item-status {
        display: none; }
      .course-curriculum ul.curriculum-sections .section-content .course-item.course-item-lp_quiz.status-started .course-item-status:before {
        content: "\f00c"; }
      .course-curriculum ul.curriculum-sections .section-content .course-item.course-item-lp_quiz .section-item-link:before {
        content: "\f017"; }
      .course-curriculum ul.curriculum-sections .section-content .course-item.course-item-lp_lesson .section-item-link:before {
        content: "\f0f6"; }
      .course-curriculum ul.curriculum-sections .section-content .course-item.course-item-lp_lesson.course-item-type-video .section-item-link:before {
        content: "\f03d"; }
      .course-curriculum ul.curriculum-sections .section-content .course-item.course-item-lp_lesson.course-item-type-audio .section-item-link:before {
        content: "\f027"; }
      .course-curriculum ul.curriculum-sections .section-content .course-item.item-preview .course-item-status {
        background: #00adff;
        font-style: normal;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px; }
        .course-curriculum ul.curriculum-sections .section-content .course-item.item-preview .course-item-status:before {
          content: attr(data-preview);
          color: #FFF;
          vertical-align: baseline; }
      .course-curriculum ul.curriculum-sections .section-content .course-item.item-locked .course-item-status {
        display: inline-block; }
        .course-curriculum ul.curriculum-sections .section-content .course-item.item-locked .course-item-status:before {
          content: "\f023";
          color: #ab6060; }
      .course-curriculum ul.curriculum-sections .section-content .course-item.has-status {
        padding-top: 1px; }
        .course-curriculum ul.curriculum-sections .section-content .course-item.has-status.status-completed .course-item-status, .course-curriculum ul.curriculum-sections .section-content .course-item.has-status.status-started .course-item-status {
          border-color: #00adff;
          color: #DDD; }
          .course-curriculum ul.curriculum-sections .section-content .course-item.has-status.status-completed .course-item-status:before, .course-curriculum ul.curriculum-sections .section-content .course-item.has-status.status-started .course-item-status:before {
            content: "\f00c"; }
        .course-curriculum ul.curriculum-sections .section-content .course-item.has-status.status-completed .course-item-status {
          color: #00adff; }
        .course-curriculum ul.curriculum-sections .section-content .course-item.has-status.passed .course-item-status {
          border-color: #00adff;
          color: #00adff; }
        .course-curriculum ul.curriculum-sections .section-content .course-item.has-status.item-failed .course-item-status, .course-curriculum ul.curriculum-sections .section-content .course-item.has-status.failed .course-item-status {
          border-color: #FF0000;
          color: #FF0000; }
          .course-curriculum ul.curriculum-sections .section-content .course-item.has-status.item-failed .course-item-status:before, .course-curriculum ul.curriculum-sections .section-content .course-item.has-status.failed .course-item-status:before {
            content: "\f00d"; }
      .course-curriculum ul.curriculum-sections .section-content .course-item:before {
        background: #00adff;
        height: 0;
        left: 0;
        top: 50%;
        position: absolute;
        content: '';
        width: 3px;
        transition: height linear 0.15s, top linear 0.15s; }
      .course-curriculum ul.curriculum-sections .section-content .course-item.current {
        padding-left: 10px;
        background: #F9F9F9; }
        .course-curriculum ul.curriculum-sections .section-content .course-item.current a:before {
          left: 10px; }
        .course-curriculum ul.curriculum-sections .section-content .course-item.current:before {
          top: 0;
          height: 100%; }
      .course-curriculum ul.curriculum-sections .section-content .course-item:not(.item-preview) .course-item-status {
        font-family: fontawesome; }

body.course-item-popup {
  overflow: hidden;
  _opacity: 0; }
  body.course-item-popup #learn-press-course-curriculum {
    position: fixed;
    top: 32px;
    bottom: 0;
    left: 0;
    width: 400px;
    background: #FFF;
    border-right: 1px solid #DDD;
    overflow: auto;
    z-index: 9999; }
    body.course-item-popup #learn-press-course-curriculum .section-header {
      padding: 0 15px; }
      body.course-item-popup #learn-press-course-curriculum .section-header .section-desc {
        margin: -10px 0 5px; }
    body.course-item-popup #learn-press-course-curriculum .course-item {
      padding-left: 15px;
      padding-right: 15px; }
      body.course-item-popup #learn-press-course-curriculum .course-item a:before {
        left: 15px; }
  body.course-item-popup #learn-press-content-item {
    position: fixed;
    z-index: 9999;
    background: #FFF;
    top: 32px;
    left: 400px;
    right: 0;
    bottom: 0;
    overflow: hidden; }
    body.course-item-popup #learn-press-content-item .content-item-wrap {
      margin: 10px auto;
      max-width: 900px; }
    body.course-item-popup #learn-press-content-item .course-item-title {
      font-size: 1.4rem; }
    body.course-item-popup #learn-press-content-item .content-question-summary .review-heading {
      text-align: center; }
    body.course-item-popup #learn-press-content-item .content-question-summary .question-title {
      font-size: 1.2rem;
      margin-bottom: 10px; }
  body.course-item-popup #content-item-nav {
    position: fixed;
    z-index: 99999;
    left: 400px;
    right: 0;
    background: #F5F5F5;
    bottom: 0;
    height: 60px;
    border-top: 1px solid #DDD; }
    body.course-item-popup #content-item-nav .content-item-nav-wrap {
      max-width: 900px;
      margin: 10px auto; }
    body.course-item-popup #content-item-nav button {
      height: 40px;
      line-height: 40px;
      padding: 0 20px; }
  body.course-item-popup .comment-form-textarea {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box; }
  body.course-item-popup.wpadminbar #learn-press-course-curriculum,
  body.course-item-popup.wpadminbar #learn-press-content-item {
    top: 92px; }
  body.course-item-popup.wpadminbar #course-item-content-header {
    top: 32px; }
  body.course-item-popup.wpadminbar .content-item-description {
    margin-bottom: 20px; }
  body.course-item-popup .content-item-summary {
    margin-bottom: 50px; }
    body.course-item-popup .content-item-summary > h3 {
      margin-bottom: 20px; }
    body.course-item-popup .content-item-summary.content-item-video .entry-video {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      background: #000;
      line-height: 1; }
      body.course-item-popup .content-item-summary.content-item-video .entry-video iframe {
        width: 100%;
        margin-bottom: 0;
        max-width: 900px;
        vertical-align: top; }
  body.course-item-popup .learn-press-content-protected-message {
    margin-bottom: 50px;
    padding: 20px;
    background: #ffe0e0; }
  body.course-item-popup.content-only #learn-press-content-item {
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 9999999; }
    body.course-item-popup.content-only #learn-press-content-item #course-item-content-header,
    body.course-item-popup.content-only #learn-press-content-item #course-item-content-footer {
      display: none; }
    body.course-item-popup.content-only #learn-press-content-item .content-item-scrollable {
      bottom: 0; }

body #ifr-course-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999999;
  background: #FFF; }

body.full-screen-content-item #course-item-content-header .toggle-content-item:before {
  font: 28px/60px fontawesome;
  content: "\f066"; }

body .content-item-summary .form-button-finish-course,
body .lp-quiz-buttons .form-button-finish-course {
  float: right; }

#wpadminbar #wp-admin-bar-edit-lp_quiz .ab-item:before,
#wpadminbar #wp-admin-bar-edit-lp_lesson .ab-item:before,
#wpadminbar #wp-admin-bar-edit-lp_question .ab-item:before {
  font-family: fontawesome;
  top: 2px; }

#wpadminbar #wp-admin-bar-edit-lp_quiz .ab-item:before {
  content: "\f017"; }

#wpadminbar #wp-admin-bar-edit-lp_lesson .ab-item:before {
  content: "\f0f6"; }

#wpadminbar #wp-admin-bar-edit-lp_question .ab-item:before {
  content: "\f29c"; }

.scroll-wrapper {
  opacity: 0;
  overflow: hidden; }
  .scroll-wrapper .scroll-element {
    background: transparent; }
    .scroll-wrapper .scroll-element.scroll-y.scroll-scrolly_visible {
      transition: opacity 0.25s; }
  .scroll-wrapper:hover .scroll-element.scroll-y.scroll-scrolly_visible {
    opacity: 0.7; }

.course-remaining-time .lp-label.label-enrolled {
  font-size: inherit; }

.learn-press-course-results-progress {
  margin-right: -4%; }
  .learn-press-course-results-progress .items-progress,
  .learn-press-course-results-progress .course-progress {
    float: left;
    margin-right: 4%;
    width: 46%;
    margin-bottom: 30px; }
    .learn-press-course-results-progress .items-progress .lp-course-progress-heading,
    .learn-press-course-results-progress .course-progress .lp-course-progress-heading {
      margin-bottom: 10px; }
    .learn-press-course-results-progress .items-progress .lp-progress-bar,
    .learn-press-course-results-progress .course-progress .lp-progress-bar {
      height: 10px;
      border-radius: 5px;
      overflow: hidden;
      position: relative; }
      .learn-press-course-results-progress .items-progress .lp-progress-bar .lp-progress-value,
      .learn-press-course-results-progress .course-progress .lp-progress-bar .lp-progress-value {
        height: 10px;
        border-radius: 5px;
        margin-left: -100%;
        position: absolute;
        width: 100%; }
    .learn-press-course-results-progress .items-progress .lp-course-status .grade.failed,
    .learn-press-course-results-progress .course-progress .lp-course-status .grade.failed {
      background: #FF0000; }
    .learn-press-course-results-progress .items-progress .lp-course-status .grade.passed,
    .learn-press-course-results-progress .course-progress .lp-course-status .grade.passed {
      background: #3eadff; }
  .learn-press-course-results-progress:after {
    clear: both;
    display: block;
    content: ''; }

#course-item-content-header,
#course-item-content-footer {
  position: fixed;
  height: 60px;
  right: 0;
  line-height: 60px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

#course-item-content-header {
  top: 0;
  left: 0;
  background: #e7f7ff;
  z-index: 100;
  border-bottom: 1px solid #DDDDDD;
  padding: 0; }
  #course-item-content-header .course-item-search {
    float: left;
    width: 400px;
    padding: 15px;
    box-sizing: border-box;
    position: relative; }
    #course-item-content-header .course-item-search input {
      height: 30px;
      width: 385px;
      background: transparent;
      border: none;
      padding: 0;
      box-shadow: none;
      float: left;
      font-style: italic;
      color: #AAA; }
      #course-item-content-header .course-item-search input:focus {
        box-shadow: none;
        outline: none;
        color: #777; }
    #course-item-content-header .course-item-search button {
      background: transparent;
      border: none;
      position: absolute;
      z-index: 99;
      margin-left: -30px;
      padding: 0;
      height: 30px;
      line-height: 30px;
      font-size: 15px;
      right: 15px;
      color: #777; }
      #course-item-content-header .course-item-search button:after {
        font-family: fontawesome;
        content: "\f002"; }
    #course-item-content-header .course-item-search.has-keyword button:after {
      content: "\f00d"; }
  #course-item-content-header .course-title {
    font-size: 1.4rem;
    float: left;
    clear: none;
    height: 60px;
    line-height: 60px;
    padding: 0 15px;
    margin: 0; }
    #course-item-content-header .course-title a {
      box-shadow: none; }
    #course-item-content-header .course-title:before {
      content: '';
      display: none; }
  #course-item-content-header .form-button.lp-button-back {
    float: right;
    margin-right: 15px; }
    #course-item-content-header .form-button.lp-button-back button {
      line-height: 34px; }
  #course-item-content-header .toggle-content-item {
    display: inline-block;
    width: 60px;
    height: 60px;
    float: right;
    text-align: center;
    border-left: 1px solid #DDD; }
    #course-item-content-header .toggle-content-item:before {
      font: 28px/60px fontawesome;
      content: "\f065"; }

#course-item-content-footer {
  border-top: 1px solid #DDD;
  bottom: 0;
  left: 400px;
  padding: 15px;
  display: none; }
  #course-item-content-footer button,
  #course-item-content-footer .lp-button {
    height: 35px;
    line-height: 30px;
    padding: 0 10px;
    font-size: 14px; }

.learn-press-form.completed button:before {
  font-family: fontawesome;
  content: '\f00c';
  font-size: 18px;
  margin-right: 10px; }

.lp-course-progress {
  position: relative; }
  .lp-course-progress .lp-passing-conditional {
    position: absolute;
    height: 10px;
    width: 3px;
    background: #FF0000;
    top: 0;
    margin-left: -1px; }

@media screen and (max-width: 1300px) {
  body.course-item-popup #course-item-content-header .course-item-search {
    width: 300px; }
    body.course-item-popup #course-item-content-header .course-item-search input {
      width: 285px; }
    body.course-item-popup #course-item-content-header .course-item-search button {
      left: 300px; }
  body.course-item-popup #learn-press-course-curriculum {
    width: 300px; }
    body.course-item-popup #learn-press-course-curriculum .progress-bg {
      width: 40px; }
  body.course-item-popup #content-item-nav,
  body.course-item-popup #learn-press-content-item,
  body.course-item-popup #course-item-content-footer {
    left: 300px; }
  body.course-item-popup .section-desc {
    display: none; } }

@media screen and (max-width: 1200px) {
  body.course-item-popup #course-item-content-header .course-item-search {
    width: 300px; }
    body.course-item-popup #course-item-content-header .course-item-search input {
      width: 285px; }
    body.course-item-popup #course-item-content-header .course-item-search button {
      left: 300px; }
  body.course-item-popup #learn-press-course-curriculum {
    width: 300px; }
  body.course-item-popup #content-item-nav,
  body.course-item-popup #learn-press-content-item,
  body.course-item-popup #course-item-content-footer {
    left: 300px; }
  body.course-item-popup #learn-press-content-item .content-item-nav-wrap,
  body.course-item-popup #learn-press-content-item .content-item-wrap {
    width: 90%; } }

@media screen and (max-width: 768px) {
  body.course-item-popup #course-item-content-header .course-item-search {
    width: 200px; }
    body.course-item-popup #course-item-content-header .course-item-search input {
      width: 185px; }
    body.course-item-popup #course-item-content-header .course-item-search button {
      left: 200px; }
  body.course-item-popup #learn-press-course-curriculum {
    width: 200px; }
  body.course-item-popup #content-item-nav,
  body.course-item-popup #learn-press-content-item,
  body.course-item-popup #course-item-content-footer {
    left: 200px; }
  body.course-item-popup #learn-press-content-item .content-item-nav-wrap,
  body.course-item-popup #learn-press-content-item .content-item-wrap {
    width: 90%; }
  body.course-item-popup.wpadminbar #learn-press-content-item,
  body.course-item-popup.wpadminbar #learn-press-course-curriculum {
    top: 106px; }
  body.course-item-popup.wpadminbar #course-item-content-header {
    top: 46px; }
  .learn-press-course-results-progress {
    margin-right: 0%; }
    .learn-press-course-results-progress .items-progress,
    .learn-press-course-results-progress .course-progress {
      float: none;
      margin-right: 0%;
      width: 100%;
      margin-bottom: 20px; } }

.lp-quiz-buttons {
  margin-bottom: 20px;
  clear: both;
  display: block;
  content: ''; }

.quiz-progress {
  background: #e7f7ff;
  margin-bottom: 30px; }
  .quiz-progress .progress-items {
    display: flex; }
    .quiz-progress .progress-items .progress-item {
      flex: 1;
      position: relative;
      font-size: 15px;
      color: #777; }
      .quiz-progress .progress-items .progress-item .progress-number,
      .quiz-progress .progress-items .progress-item .progress-label {
        display: block;
        text-align: center;
        line-height: 1; }
      .quiz-progress .progress-items .progress-item .progress-number {
        font-size: 20px;
        margin: 15px 0 10px 0; }
      .quiz-progress .progress-items .progress-item .progress-label {
        font-size: 14px;
        margin-bottom: 15px; }
      .quiz-progress .progress-items .progress-item i {
        display: none;
        width: 60px;
        height: 60px;
        font-size: 30px;
        text-align: center;
        line-height: 60px;
        color: #FFF;
        background: #00adff;
        float: left; }
      .quiz-progress .progress-items .progress-item:after {
        clear: both;
        display: block;
        content: ''; }

.answer-options {
  list-style: none;
  margin: 0;
  padding: 0; }
  .answer-options .answer-option {
    margin: 0 0 15px 0;
    position: relative;
    background: #F5F5F5;
    padding: 10px;
    display: flex;
    overflow: hidden;
    color: #777;
    cursor: pointer;
    font-size: 20px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    -webkit-transition: background linear 0.25s;
    -moz-transition: background linear 0.25s;
    transition: background linear 0.25s; }
    .answer-options .answer-option .option-title {
      font-size: smaller;
      display: table-cell; }
      .answer-options .answer-option .option-title .option-title-content {
        display: inline-block;
        vertical-align: middle; }
      .answer-options .answer-option .option-title:before {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 3px;
        content: '';
        background: #DDD;
        -webkit-transition: background linear 0.25s;
        -moz-transition: background linear 0.25s;
        transition: background linear 0.25s; }
    .answer-options .answer-option input[type="checkbox"],
    .answer-options .answer-option input[type="radio"] {
      -webkit-appearance: initial;
      -moz-appearance: initial;
      margin: 0 10px 0 3px;
      position: relative;
      border: 1px solid #eaeaea;
      z-index: 10;
      width: 32px;
      height: 32px;
      min-width: 32px;
      background: #FFF;
      -webkit-border-radius: 4px;
      -moz-border-radius: 4px;
      border-radius: 4px; }
      .answer-options .answer-option input[type="checkbox"]:focus,
      .answer-options .answer-option input[type="radio"]:focus {
        outline: none; }
      .answer-options .answer-option input[type="checkbox"]:after,
      .answer-options .answer-option input[type="radio"]:after {
        content: '';
        border: 2px solid #00adff;
        display: inline-block;
        width: 7px;
        height: 15px;
        border-top: none;
        border-left: none;
        position: absolute;
        top: 50%;
        left: 10px;
        box-sizing: content-box;
        opacity: 0;
        margin-top: -10px;
        -webkit-transform: rotate(36deg) scale(0);
        -moz-transform: rotate(36deg) scale(0);
        -ms-transform: rotate(36deg) scale(0);
        -o-transform: rotate(36deg) scale(0);
        transform: rotate(36deg) scale(0);
        -webkit-transition: all 0.25s;
        -moz-transition: all 0.25s;
        transition: all 0.25s; }
      .answer-options .answer-option input[type="checkbox"]:checked ~ .option-title .option-title-content,
      .answer-options .answer-option input[type="radio"]:checked ~ .option-title .option-title-content {
        position: relative; }
      .answer-options .answer-option input[type="checkbox"]:checked ~ .option-title:before,
      .answer-options .answer-option input[type="radio"]:checked ~ .option-title:before {
        background: #00adff; }
      .answer-options .answer-option input[type="checkbox"]:checked:after,
      .answer-options .answer-option input[type="radio"]:checked:after {
        opacity: 1;
        -webkit-transform: rotate(36deg) scale(1);
        -moz-transform: rotate(36deg) scale(1);
        -ms-transform: rotate(36deg) scale(1);
        -o-transform: rotate(36deg) scale(1);
        transform: rotate(36deg) scale(1); }
    .answer-options .answer-option input[type="radio"] {
      -webkit-border-radius: 50%;
      -moz-border-radius: 50%;
      border-radius: 50%; }
      .answer-options .answer-option input[type="radio"]:before {
        border-radius: 50%; }
    .answer-options .answer-option .option-title {
      margin: 0; }
    .answer-options .answer-option:hover {
      background: #e1f5ff; }
    .answer-options .answer-option.answer-correct {
      background: #e1f5ff; }
      .answer-options .answer-option.answer-correct input[type="radio"]:checked ~ .option-title:before,
      .answer-options .answer-option.answer-correct input[type="checkbox"]:checked ~ .option-title:before {
        background: #e1f5ff; }
    .answer-options .answer-option.answered-correct input[type="radio"]:checked ~ .option-title:before,
    .answer-options .answer-option.answered-correct input[type="checkbox"]:checked ~ .option-title:before {
      background: #00adff; }
    .answer-options .answer-option.answered-wrong input[type="radio"]:before, .answer-options .answer-option.answered-wrong input[type="radio"]:after,
    .answer-options .answer-option.answered-wrong input[type="checkbox"]:before,
    .answer-options .answer-option.answered-wrong input[type="checkbox"]:after {
      border-color: #FF0000; }
    .answer-options .answer-option.answered-wrong input[type="radio"]:checked ~ .option-title:before,
    .answer-options .answer-option.answered-wrong input[type="checkbox"]:checked ~ .option-title:before {
      background: #FF0000; }

button[data-counter] {
  position: relative; }
  button[data-counter]:after {
    content: "(+" attr(data-counter) ")";
    padding-left: 5px; }

.quiz-result {
  max-width: 400px;
  margin: 30px auto 20px;
  text-align: center; }
  .quiz-result .result-grade .result-achieved,
  .quiz-result .result-grade .result-require {
    display: inline-block;
    margin: 0 auto; }
  .quiz-result .result-grade .result-achieved {
    font-size: 36px;
    border-bottom: 2px solid #04adff; }
  .quiz-result .result-grade .result-require {
    display: block; }
  .quiz-result .result-grade .result-message {
    font-size: 14px; }
  .quiz-result.failed .result-achieved {
    color: #c55757; }
  .quiz-result.failed .result-message strong {
    color: #c55757; }
  .quiz-result.passed .result-achieved {
    color: #04adff; }
  .quiz-result.passed .result-message strong {
    color: #04adff; }
  .quiz-result .result-statistic {
    list-style: none;
    text-align: left;
    margin: 0;
    padding: 0; }
    .quiz-result .result-statistic .result-statistic-field {
      display: flex; }
      .quiz-result .result-statistic .result-statistic-field label, .quiz-result .result-statistic .result-statistic-field p {
        flex: 1;
        margin: 0; }
      .quiz-result .result-statistic .result-statistic-field label {
        font-weight: bold; }
      .quiz-result .result-statistic .result-statistic-field p {
        text-align: right; }

.question-numbers {
  list-style: none;
  text-align: center; }
  .question-numbers li {
    display: inline-block;
    position: relative;
    margin-bottom: 3px; }
    .question-numbers li a {
      display: block;
      padding: 8px;
      background: #F5F5F5;
      line-height: 1;
      color: #999;
      border: 1px solid #DDD;
      font-size: 12px;
      min-width: 20px;
      box-shadow: none; }
      .question-numbers li a span {
        vertical-align: middle; }
      .question-numbers li a:hover {
        background: #00adff;
        color: #FFF;
        border: 1px solid #3880a2; }
    .question-numbers li.current a {
      background: #00adff;
      color: #FFF !important;
      border-color: #3880a2; }
    .question-numbers li.current.skipped:after {
      background: #FFF; }
    .question-numbers li.answered a:after {
      font-family: fontawesome;
      font-size: 8px;
      vertical-align: middle;
      margin-left: 3px; }
    .question-numbers li.answered.answered-wrong a {
      color: #FF0000; }
    .question-numbers li.answered.answered-true a {
      color: #00adff; }
    .question-numbers li.answered.answered-true.current a {
      color: #FFF; }
    .question-numbers li.skipped:after {
      content: '';
      background: #AAA;
      width: 10px;
      height: 4px;
      position: absolute;
      border-radius: 2px;
      left: 50%;
      margin-left: -5px;
      bottom: 3px; }

.quiz-intro {
  list-style: none;
  margin: 0 0 20px;
  padding: 0;
  display: table; }
  .quiz-intro li {
    margin: 0;
    display: table-row; }
    .quiz-intro li label, .quiz-intro li span {
      display: table-cell; }
    .quiz-intro li label {
      font-weight: bold;
      padding: 0 20px 10px 0px;
      line-height: 1; }
    .quiz-intro li span {
      text-align: left; }

.question-explanation-content,
.question-hint-content {
  margin-bottom: 20px;
  background: #F5F5F5;
  padding: 10px 15px; }

.redo-quiz button[type="submit"] {
  content: attr(data-counter); }

.circle-bar {
  position: relative;
  width: 300px;
  height: 300px;
  border-color: #DDD; }
  .circle-bar:before {
    border-radius: 50%;
    border: 10px solid #DDD;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 0;
    content: '';
    box-sizing: border-box; }
  .circle-bar .before,
  .circle-bar .after {
    border-radius: 50%;
    border: 10px solid #14c4ff;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 0;
    box-sizing: border-box; }
  .circle-bar .before {
    border-bottom-color: transparent;
    border-left-color: transparent;
    transform: rotate(45deg); }
  .circle-bar .after {
    border-color: #14c4ff;
    border-right-color: transparent;
    border-top-color: transparent;
    transform: rotate(45deg); }
  .circle-bar.bg50 .after {
    transform: rotate(45deg);
    z-index: 10;
    border-left-color: inherit;
    border-bottom-color: inherit; }

.learn-press-message.fixed {
  position: fixed;
  top: 32px;
  left: 0;
  right: 0;
  background: rgba(0, 173, 255, 0.6);
  text-align: center;
  z-index: 100;
  color: #FFF;
  padding: 10px; }
  .learn-press-message.fixed[data-delay-in] {
    display: none; }

/**
 * CSS for overriding some style defaults of themes
 */
/**/
body.twentysixteen.learnpress-page .entry-footer {
  display: none; }

body.twentysixteen.learnpress-page .entry-content {
  float: none;
  width: auto; }

@media screen and (min-width: 61.5625em) {
  body.twentysixteen.learnpress-page .entry-footer {
    display: none; }
  body.twentysixteen.learnpress-page .entry-content {
    float: none;
    width: auto; }
  body:not(.search-results) article:not(.type-page) .entry-footer {
    display: none; }
  body:not(.search-results) article:not(.type-page) .entry-content {
    float: none;
    width: auto; } }

body.twentyseventeen.learnpress-page #primary article.page .entry-header,
body.twentyseventeen.learnpress-page #primary article.page .entry-content {
  width: 100%;
  float: none; }

@media screen and (min-width: 48em) {
  body.twentyseventeen.learnpress-page #primary article.page .entry-header,
  body.twentyseventeen.learnpress-page #primary article.page .entry-content {
    width: 100%;
    float: none; } }

.lp-widget .items-progress,
.lp-widget .course-progress {
  width: 100%; }

.lp-widget .course-remaining-time {
  font-size: 14px; }

.lp-widget .lp-course-info-fields {
  margin: 0; }
  .lp-widget .lp-course-info-fields .lp-course-info {
    list-style: none;
    margin: 0 0 10px 0; }
    .lp-widget .lp-course-info-fields .lp-course-info .lp-label {
      float: right;
      background: #03a9f4; }

.learn-press-tip {
  display: none;
  line-height: 1;
  margin: 0 5px;
  font-size: 16px;
  vertical-align: baseline;
  cursor: pointer;
  font-style: normal; }
  .learn-press-tip.ready {
    display: inline-block; }
  .learn-press-tip:before {
    font-family: Fontawesome;
    content: "\f059"; }
  .learn-press-tip:hover {
    color: #0073aa; }

.learn-press-tip-floating {
  background: rgba(39, 115, 170, 0.9);
  padding: 8px 10px;
  line-height: 1.2em;
  color: #FFF;
  min-width: 20px;
  position: absolute;
  margin-left: -1px;
  font-size: 12px;
  z-index: 9999;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px; }
  .learn-press-tip-floating .close {
    display: inline-block;
    position: absolute;
    width: 16px;
    height: 16px;
    right: -8px;
    top: -8px;
    border: 1px solid #FFF;
    text-align: center;
    line-height: 16px;
    background: #468fbc;
    color: #FFF;
    cursor: pointer;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%; }
    .learn-press-tip-floating .close:before {
      content: "\f00d";
      font-family: Fontawesome; }
  .learn-press-tip-floating p {
    margin: 0; }
  .learn-press-tip-floating:before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    bottom: -7px;
    border: 7px solid transparent;
    border-top-color: rgba(39, 115, 170, 0.9);
    border-bottom-width: 0;
    left: 50%;
    margin-left: -7px; }

body.lp-preview.admin-bar #learn-press-content-item {
  top: 32px !important; }

body.lp-preview #learn-press-course-curriculum {
  display: none; }

body.lp-preview #learn-press-content-item {
  top: 0 !important;
  left: 0 !important; }

body.lp-preview #course-item-content-header {
  display: none; }

/**
 * CSS for jAlerts
 */
/**
 * Mixin
 */
@-webkit-keyframes rotating4 /* Safari and Chrome */ {
  from {
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg); }
  to {
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg); } }

@keyframes rotating4 {
  from {
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg); }
  to {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg); } }

@-webkit-keyframes animation4 /* Safari and Chrome */ {
  from {
    left: -40%;
    width: 40%; }
  to {
    left: 100%;
    width: 10%; } }

@keyframes animation4 {
  from {
    left: -40%;
    width: 40%; }
  to {
    left: 100%;
    width: 10%; } }

#popup_container {
  opacity: 0;
  transform: scale(0.5); }

body.confirm #popup_overlay {
  z-index: 999998 !important; }

body.confirm #popup_container {
  z-index: 999999 !important;
  max-width: 90% !important;
  min-width: 300px !important;
  padding: 10px !important;
  background: #F5F5F5;
  transition: opacity 0.25s;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px; }
  body.confirm #popup_container #popup_title {
    display: none !important; }
  body.confirm #popup_container #popup_message {
    margin: -10px;
    background: #FFF;
    padding: 20px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px; }
  body.confirm #popup_container .close {
    position: absolute;
    top: 3px;
    right: 10px; }
  body.confirm #popup_container #popup_panel {
    margin-top: 20px;
    text-align: center; }
    body.confirm #popup_container #popup_panel button, body.confirm #popup_container #popup_panel input[type="button"], body.confirm #popup_container #popup_panel input[type="submit"] {
      height: 30px;
      line-height: 30px;
      padding: 0 25px; }
  body.confirm #popup_container #popup_cancel {
    display: none; }
  body.confirm #popup_container.ready {
    opacity: 1;
    transform: scale(1); }

input[type="text"],
input[type="email"],
input[type="number"],
input[type="password"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box; }

.hide-if-js {
  display: none !important; }

.clearfix:after {
  clear: both;
  display: block;
  content: ''; }

.learn-press-breadcrumb {
  margin-bottom: 20px; }

.learnpress-page .lp-button {
  line-height: 45px;
  padding: 0 12px;
  height: 45px; }

.course-meta {
  margin-bottom: 20px; }

#learn-press-course-tabs {
  margin-bottom: 40px; }

/* Archive */
form[name="search-course"] {
  margin-bottom: 20px;
  position: relative; }
  form[name="search-course"] .search-course-input {
    width: 100%;
    padding: 12px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box; }
  form[name="search-course"] .search-course-button {
    position: absolute;
    top: 1px;
    right: 1px;
    bottom: 1px;
    padding: 15px;
    height: auto;
    line-height: 1px; }

ul.learn-press-courses {
  list-style: none;
  margin-right: -2.1%;
  margin-left: 0;
  padding: 0;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -moz-flex;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  flex-wrap: wrap; }
  ul.learn-press-courses .course {
    list-style: none;
    width: 31.33333%;
    margin: 0 2% 20px 0;
    box-shadow: none;
    padding: 0; }
    ul.learn-press-courses .course .course-thumbnail,
    ul.learn-press-courses .course .course-title,
    ul.learn-press-courses .course .course-info {
      margin: 0 0 0.8em 0;
      line-height: 1; }
    ul.learn-press-courses .course .course-thumbnail img {
      line-height: 1;
      vertical-align: top; }
    ul.learn-press-courses .course .course-title {
      font-size: 1.2em;
      padding: 0;
      font-weight: normal; }
    ul.learn-press-courses .course .course-info {
      font-size: 14px; }
      ul.learn-press-courses .course .course-info > span {
        display: block; }
      ul.learn-press-courses .course .course-info .course-price {
        float: left; }
        ul.learn-press-courses .course .course-info .course-price .origin-price,
        ul.learn-press-courses .course .course-info .course-price .price {
          vertical-align: middle; }
        ul.learn-press-courses .course .course-info .course-price .origin-price {
          font-size: 16px;
          text-decoration: line-through;
          font-style: italic;
          margin-right: 10px; }
        ul.learn-press-courses .course .course-info .course-price .price {
          font-size: 18px; }
      ul.learn-press-courses .course .course-info .course-instructor {
        float: right; }
      ul.learn-press-courses .course .course-info:after {
        clear: both;
        display: block;
        content: ''; }
    ul.learn-press-courses .course .lp-course-buttons form {
      width: 100%;
      margin-bottom: 10px; }
      ul.learn-press-courses .course .lp-course-buttons form button {
        width: 100%; }

.order-comments {
  width: 100%;
  min-height: 150px; }

.learn-press-progress {
  position: relative; }
  .learn-press-progress .progress-bg {
    background: #DDD;
    height: 10px;
    position: relative;
    overflow: hidden;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px; }
    .learn-press-progress .progress-bg .progress-active {
      height: 100%;
      width: 100%;
      position: absolute;
      left: 50%;
      margin-left: -100%;
      background: #00adff;
      -webkit-border-radius: 5px;
      -moz-border-radius: 5px;
      border-radius: 5px; }

/**
 * Forms
 */
.retake-course,
.enroll-course,
.purchase-course {
  display: inline-block; }

/* */
.table-orders {
  font-size: 16px; }
  .table-orders th, .table-orders td {
    padding: 5px 10px; }

.form-button {
  display: inline-block; }

.learn-press-pagination {
  text-align: center;
  margin-bottom: 20px; }
  .learn-press-pagination .page-numbers {
    display: inline-block;
    list-style: none;
    margin: 0; }
    .learn-press-pagination .page-numbers > li {
      display: inline-block;
      margin: 0; }
      .learn-press-pagination .page-numbers > li a, .learn-press-pagination .page-numbers > li span {
        display: block;
        padding: 5px 10px;
        border: 1px solid #DDD;
        box-shadow: none;
        line-height: 1;
        float: none;
        font-size: 1em;
        border-radius: 0; }
      .learn-press-pagination .page-numbers > li span {
        background: #F5F5F5; }

.learn-press-message {
  padding: 10px 12px 10px 15px;
  background: #F5F5F5;
  position: relative;
  margin: 0 0 20px 0;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px; }
  .learn-press-message.icon {
    padding-left: 45px; }
    .learn-press-message.icon:after {
      font-size: 20px;
      vertical-align: baseline;
      margin-right: 10px;
      position: absolute;
      left: 15px;
      top: 50%;
      margin-top: -13px;
      height: 26px;
      line-height: 26px;
      background: #04adff;
      width: 26px;
      text-align: center;
      border-radius: 50%;
      color: #FFF;
      font-family: FontAwesome; }
  .learn-press-message:before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background: #00adff;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px; }
  .learn-press-message.error:before {
    background: #d85554; }
  .learn-press-message.error.icon:before {
    background: #d85554; }
  .learn-press-message.error.icon:after {
    content: "\f00d";
    background: #d85554; }
  .learn-press-message.success.icon:before {
    background: #00adff; }
  .learn-press-message.success.icon:after {
    content: "\f129";
    background: #00adff; }

ul.list-table-nav {
  display: flex;
  list-style: none;
  margin-left: 0; }
  ul.list-table-nav .nav-text {
    flex: 1;
    text-align: left; }
  ul.list-table-nav .nav-pages {
    flex: 1;
    text-align: right; }
    ul.list-table-nav .nav-pages .learn-press-pagination {
      display: inline-block;
      margin-bottom: 0; }

.primary-color {
  color: #00adff; }

.primary-color-before:before {
  color: #00adff; }

.primary-color-after:after {
  color: #00adff; }

.primary-background-color {
  background: #00adff; }

.primary-background-color {
  background: #00adff; }

.course-origin-price {
  font-size: 85%;
  text-decoration: line-through;
  margin-right: 5px; }

.course-item-nav {
  display: flex; }
  .course-item-nav .prev, .course-item-nav .next {
    flex: 1; }
    .course-item-nav .prev span, .course-item-nav .next span {
      display: block;
      font-weight: bold; }
    .course-item-nav .prev a, .course-item-nav .next a {
      color: #999999; }
  .course-item-nav .next {
    text-align: right; }

.content-item-wrap #comments {
  margin-left: 0;
  margin-right: 0; }
  .content-item-wrap #comments #comment {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box; }

@media screen and (max-width: 768px) {
  ul.learn-press-courses .course {
    width: 48%; } }

@media screen and (max-width: 600px) {
  ul.learn-press-courses {
    margin-right: 0; }
    ul.learn-press-courses .course {
      width: 100%;
      margin-right: 0; } }
