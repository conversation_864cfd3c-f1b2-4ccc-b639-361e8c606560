const Ziggy = {"url":"http:\/\/localsite.test","port":null,"defaults":{},"routes":{"sanctum.csrf-cookie":{"uri":"sanctum\/csrf-cookie","methods":["GET","HEAD"]},"ignition.healthCheck":{"uri":"_ignition\/health-check","methods":["GET","HEAD"]},"ignition.executeSolution":{"uri":"_ignition\/execute-solution","methods":["POST"]},"ignition.updateConfig":{"uri":"_ignition\/update-config","methods":["POST"]},"index":{"uri":"\/","methods":["GET","HEAD"]},"speakers.index":{"uri":"speakers","methods":["GET","HEAD"]},"speakers.show":{"uri":"speakers\/{speaker}","methods":["GET","HEAD"],"parameters":["speaker"],"bindings":{"speaker":"slug"}},"location.show":{"uri":"location\/{location}","methods":["GET","HEAD"],"parameters":["location"],"bindings":{"location":"slug"}},"location.speakers.search":{"uri":"location\/{location}\/search","methods":["GET","HEAD"],"parameters":["location"],"bindings":{"location":"slug"}},"gallery.index":{"uri":"gallery","methods":["GET","HEAD"]},"blogs.index":{"uri":"blogs","methods":["GET","HEAD"]},"blogs.show":{"uri":"blogs\/{blog}","methods":["GET","HEAD"],"parameters":["blog"],"bindings":{"blog":"slug"}},"pages.profile":{"uri":"profile","methods":["GET","HEAD"]},"pages.profile.arabic":{"uri":"profile-arabic","methods":["GET","HEAD"]},"faqs.index":{"uri":"faqs","methods":["GET","HEAD"]},"pages.terms":{"uri":"page\/terms-condition","methods":["GET","HEAD"]},"pages.contact":{"uri":"contact","methods":["GET","HEAD"]},"pages.policy":{"uri":"page\/refund-policy","methods":["GET","HEAD"]},"external.sports":{"uri":"external\/sports","methods":["GET","HEAD"]},"external.wellness":{"uri":"external\/wellness","methods":["GET","HEAD"]},"external.coaching":{"uri":"external\/coaching","methods":["GET","HEAD"]},"sitemap":{"uri":"sitemap","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"admin.dashboard":{"uri":"crm","methods":["GET","HEAD"]},"admin.profiles.index":{"uri":"crm\/profiles","methods":["GET","HEAD"]},"admin.profiles.search":{"uri":"crm\/profiles\/search","methods":["POST"]},"admin.profiles.create":{"uri":"crm\/profiles\/create","methods":["GET","HEAD"]},"admin.profiles.show":{"uri":"crm\/profiles\/{profile}","methods":["GET","HEAD"],"parameters":["profile"],"bindings":{"profile":"hash_id"}},"admin.profiles.store":{"uri":"crm\/profiles","methods":["POST"]},"admin.profiles.edit":{"uri":"crm\/profiles\/{profile}\/edit","methods":["GET","HEAD"],"parameters":["profile"],"bindings":{"profile":"hash_id"}},"admin.profiles.update":{"uri":"crm\/profiles\/{profile}","methods":["POST"],"parameters":["profile"],"bindings":{"profile":"hash_id"}},"admin.profiles.delete":{"uri":"crm\/profiles\/{profile}\/delete","methods":["POST"],"parameters":["profile"],"bindings":{"profile":"hash_id"}},"admin.portfolios.index":{"uri":"crm\/portfolios","methods":["GET","HEAD"]},"admin.portfolios.store":{"uri":"crm\/portfolios","methods":["POST"]},"admin.portfolios.create":{"uri":"crm\/portfolios\/create","methods":["GET","HEAD"]},"admin.portfolios.edit":{"uri":"crm\/portfolios\/{portfolio}\/edit","methods":["GET","HEAD"],"parameters":["portfolio"],"bindings":{"portfolio":"hash_id"}},"admin.portfolios.update":{"uri":"crm\/portfolios\/{portfolio}","methods":["POST"],"parameters":["portfolio"]},"admin.portfolios.delete":{"uri":"crm\/portfolios\/{portfolio}\/delete","methods":["POST"],"parameters":["portfolio"]},"admin.rate-cards.index":{"uri":"crm\/rate-cards","methods":["GET","HEAD"]},"admin.rate-cards.store":{"uri":"crm\/rate-cards","methods":["POST"]},"admin.rate-cards.create":{"uri":"crm\/rate-cards\/create","methods":["GET","HEAD"]},"admin.rate-cards.edit":{"uri":"crm\/rate-cards\/{rateCard}\/edit","methods":["GET","HEAD"],"parameters":["rateCard"]},"admin.rate-cards.update":{"uri":"crm\/rate-cards\/{rateCard}","methods":["POST"],"parameters":["rateCard"]},"admin.rate-cards.delete":{"uri":"crm\/rate-cards\/{rateCard}\/delete","methods":["POST"],"parameters":["rateCard"]},"admin.profiles.videos":{"uri":"crm\/profiles\/{profile}\/videos","methods":["GET","HEAD"],"parameters":["profile"],"bindings":{"profile":"slug"}},"admin.profiles.videos.store":{"uri":"crm\/profiles\/{profile}\/videos","methods":["POST"],"parameters":["profile"],"bindings":{"profile":"slug"}},"admin.profiles.videos.destroy":{"uri":"crm\/profiles\/videos\/{video}\/delete","methods":["POST"],"parameters":["video"],"bindings":{"video":"id"}},"admin.profiles.rate-cards":{"uri":"crm\/profiles\/{profile}\/rate-cards","methods":["GET","HEAD"],"parameters":["profile"],"bindings":{"profile":"hash_id"}},"admin.profiles.rate-cards.store":{"uri":"crm\/profiles\/{profile}\/rate-cards","methods":["POST"],"parameters":["profile"]},"admin.profiles.rate-cards.destroy":{"uri":"crm\/profiles\/{portfolio}\/rate-cards\/delete","methods":["POST"],"parameters":["portfolio"]},"admin.profiles.rate-cards.update":{"uri":"crm\/profiles\/{portfolio}\/rate-cards\/update","methods":["POST"],"parameters":["portfolio"]},"admin.profiles.rate-cards.edit":{"uri":"crm\/profiles\/rate-cards\/{portfolio}\/edit","methods":["GET","HEAD"],"parameters":["portfolio"]},"admin.profiles.proposals":{"uri":"crm\/profile\/{profile}\/proposals","methods":["GET","HEAD"],"parameters":["profile"],"bindings":{"profile":"hash_id"}},"admin.profiles.media":{"uri":"crm\/profile\/{profile}\/media","methods":["GET","HEAD"],"parameters":["profile"],"bindings":{"profile":"hash_id"}},"admin.profiles.media.store":{"uri":"crm\/profile\/{profile}\/media","methods":["POST"],"parameters":["profile"],"bindings":{"profile":"hash_id"}},"admin.profiles.portfolios":{"uri":"crm\/profile\/{profile}\/portfolios","methods":["GET","HEAD"],"parameters":["profile"],"bindings":{"profile":"hash_id"}},"admin.speakers.index":{"uri":"crm\/speakers","methods":["GET","HEAD"]},"admin.speakers.search":{"uri":"crm\/speakers\/search","methods":["POST"]},"admin.speakers.create":{"uri":"crm\/speakers\/create","methods":["GET","HEAD"]},"admin.speakers.show":{"uri":"crm\/speakers\/{speaker}","methods":["GET","HEAD"],"parameters":["speaker"],"bindings":{"speaker":"slug"}},"admin.speakers.store":{"uri":"crm\/speakers","methods":["POST"]},"admin.speakers.edit":{"uri":"crm\/speakers\/{speaker}\/edit","methods":["GET","HEAD"],"parameters":["speaker"],"bindings":{"speaker":"slug"}},"admin.speakers.update":{"uri":"crm\/speakers\/{speaker}","methods":["POST"],"parameters":["speaker"]},"admin.speakers.delete":{"uri":"crm\/speakers\/{speaker}\/delete","methods":["POST"],"parameters":["speaker"]},"admin.speakers.videos":{"uri":"crm\/speakers\/{speakers}\/videos","methods":["GET","HEAD"],"parameters":["speakers"]},"admin.speakers.videos.store":{"uri":"crm\/speakers\/{speakers}\/videos","methods":["POST"],"parameters":["speakers"]},"admin.speakers.videos.destroy":{"uri":"crm\/speakers\/videos\/{video}\/delete","methods":["POST"],"parameters":["video"],"bindings":{"video":"id"}},"admin.referrals.index":{"uri":"crm\/referrals","methods":["GET","HEAD"]},"admin.referrals.store":{"uri":"crm\/referrals","methods":["POST"]},"admin.referrals.create":{"uri":"crm\/referrals\/create","methods":["GET","HEAD"]},"admin.referrals.edit":{"uri":"crm\/referrals\/{referral}\/edit","methods":["GET","HEAD"],"parameters":["referral"]},"admin.referrals.update":{"uri":"crm\/referrals\/{referral}","methods":["POST"],"parameters":["referral"]},"admin.referrals.delete":{"uri":"crm\/referrals\/{referral}\/delete","methods":["POST"],"parameters":["referral"]},"admin.gallery.index":{"uri":"crm\/gallery","methods":["GET","HEAD"]},"admin.gallery.store":{"uri":"crm\/gallery","methods":["POST"]},"admin.gallery.delete":{"uri":"crm\/gallery\/{gallery}","methods":["POST"],"parameters":["gallery"],"bindings":{"gallery":"id"}},"admin.blogs.index":{"uri":"crm\/blogs","methods":["GET","HEAD"]},"admin.blogs.store":{"uri":"crm\/blogs","methods":["POST"]},"admin.blogs.create":{"uri":"crm\/blogs\/create","methods":["GET","HEAD"]},"admin.blogs.edit":{"uri":"crm\/blogs\/{blog}\/edit","methods":["GET","HEAD"],"parameters":["blog"],"bindings":{"blog":"slug"}},"admin.blogs.update":{"uri":"crm\/blogs\/{blog}","methods":["POST"],"parameters":["blog"],"bindings":{"blog":"slug"}},"admin.blogs.delete":{"uri":"crm\/blogs\/{blog}\/delete","methods":["POST"],"parameters":["blog"],"bindings":{"blog":"slug"}},"admin.settings.location":{"uri":"crm\/settings\/location","methods":["GET","HEAD"]},"admin.settings.location.store":{"uri":"crm\/settings\/location","methods":["POST"]},"admin.deals.index":{"uri":"crm\/deals","methods":["GET","HEAD"]},"admin.deals.store":{"uri":"crm\/deals","methods":["POST"]},"admin.deals.create":{"uri":"crm\/deals\/create","methods":["GET","HEAD"]},"admin.deals.edit":{"uri":"crm\/deals\/{deal}\/edit","methods":["GET","HEAD"],"parameters":["deal"]},"admin.deals.update":{"uri":"crm\/deals\/{deal}","methods":["POST"],"parameters":["deal"]},"admin.deals.delete":{"uri":"crm\/deals\/{deal}\/delete","methods":["POST"],"parameters":["deal"]},"admin.proposals.index":{"uri":"crm\/proposals","methods":["GET","HEAD"]},"admin.proposals.store":{"uri":"crm\/proposals","methods":["POST"]},"admin.proposals.create":{"uri":"crm\/proposals\/create","methods":["GET","HEAD"]},"admin.proposals.show":{"uri":"crm\/proposals\/{proposal}","methods":["GET","HEAD"],"parameters":["proposal"]},"admin.proposals.portfolios.suggest":{"uri":"crm\/proposals\/suggest\/portfolios","methods":["GET","HEAD"]},"admin.proposals.edit":{"uri":"crm\/proposals\/{proposal}\/edit","methods":["GET","HEAD"],"parameters":["proposal"]},"admin.proposals.update":{"uri":"crm\/proposals\/{proposal}","methods":["POST"],"parameters":["proposal"]},"admin.proposals.delete":{"uri":"crm\/proposals\/{proposal}\/delete","methods":["POST"],"parameters":["proposal"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"password.update":{"uri":"password","methods":["PUT"]},"logout":{"uri":"logout","methods":["POST"]}}};

if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
    Object.assign(Ziggy.routes, window.Ziggy.routes);
}

export { Ziggy };
