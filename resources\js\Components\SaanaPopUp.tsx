import { useState, useEffect, useRef } from "react";
import { XMarkIcon } from "@heroicons/react/24/outline";

export default function HomePopup() {
  const [isVisible, setIsVisible] = useState(false);
  const [formLoaded, setFormLoaded] = useState(false);
  const formContainerRef = useRef(null);

  useEffect(() => {
    // Show popup after a short delay when the page loads
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 1500); // 1.5 second delay

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Load Bitrix form script when the popup becomes visible
    if (isVisible && !formLoaded && formContainerRef.current) {
      const script = document.createElement("script");
      script.setAttribute("data-b24-form", "inline/125/k1rhtz");
      script.setAttribute("data-skip-moving", "true");
      script.innerHTML = `
                (function(w,d,u){
                    var s=d.createElement('script');
                    s.async=true;
                    s.src=u+'?'+(Date.now()/180000|0);
                    var h=d.getElementsByTagName('script')[0];
                    h.parentNode.insertBefore(s,h);
                })(window,document,'https://cdn.bitrix24.com/b25531643/crm/form/loader_125.js');
            `;

      formContainerRef.current.appendChild(script);
      setFormLoaded(true);
    }
  }, [isVisible, formLoaded]);

  const handleClose = () => {
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black bg-opacity-70 transition-opacity"
        onClick={handleClose}
      ></div>

      <div className="relative z-10 w-full max-w-5xl transform overflow-hidden rounded-lg bg-white shadow-xl transition-all">
        <div className="absolute right-4 top-4">
          <button
            type="button"
            className="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none"
            onClick={handleClose}
          >
            <span className="sr-only">Close</span>
            <XMarkIcon className="h-6 w-6" aria-hidden="true" />
          </button>
        </div>

        <div className="flex flex-col md:flex-row">
          {/* Bitrix Form - Left Side */}
          <div className="w-full p-8 md:w-1/2">
            <h3 className="mb-6 font-besley text-2xl font-semibold text-gray-900">
              🎤 Command the Room: Masterclass — Dubai, June 2025
            </h3>
            <p className="mb-6 text-gray-600">
              Join renowned speaker Saana Azzam for a 2-day masterclass to
              unlock the mindset, presence, and tools to speak with impact.
            </p>

            {/* Bitrix Form Container */}
            <div ref={formContainerRef} className="bitrix-form-container">
              {/* Bitrix form will be loaded here */}
            </div>

            <p className="mt-6 text-gray-600">
              🔥 Early bird access now open — limited seats, high demand.
            </p>
          </div>

          {/* Image - Right Side */}
          <div className="hidden w-1/2 bg-gray-100 md:block">
            <img
              src="/images/pop-up.jpeg"
              alt="Saana Azzam"
              className="h-full w-full object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
