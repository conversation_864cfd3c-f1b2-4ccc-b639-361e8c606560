/* This file is generated by Ziggy. */
declare module 'ziggy-js' {
  interface RouteList {
    "sanctum.csrf-cookie": [],
    "ignition.healthCheck": [],
    "ignition.executeSolution": [],
    "ignition.updateConfig": [],
    "index": [],
    "speakers.index": [],
    "speakers.show": [
        {
            "name": "speaker",
            "binding": "slug"
        }
    ],
    "location.show": [
        {
            "name": "location",
            "binding": "slug"
        }
    ],
    "location.speakers.search": [
        {
            "name": "location",
            "binding": "slug"
        }
    ],
    "gallery.index": [],
    "blogs.index": [],
    "blogs.show": [
        {
            "name": "blog",
            "binding": "slug"
        }
    ],
    "pages.profile": [],
    "pages.profile.arabic": [],
    "faqs.index": [],
    "pages.terms": [],
    "pages.contact": [],
    "pages.policy": [],
    "external.sports": [],
    "external.wellness": [],
    "external.coaching": [],
    "sitemap": [],
    "dashboard": [],
    "admin.dashboard": [],
    "admin.profiles.index": [],
    "admin.profiles.search": [],
    "admin.profiles.create": [],
    "admin.profiles.show": [
        {
            "name": "profile",
            "binding": "hash_id"
        }
    ],
    "admin.profiles.store": [],
    "admin.profiles.edit": [
        {
            "name": "profile",
            "binding": "hash_id"
        }
    ],
    "admin.profiles.update": [
        {
            "name": "profile",
            "binding": "hash_id"
        }
    ],
    "admin.profiles.delete": [
        {
            "name": "profile",
            "binding": "hash_id"
        }
    ],
    "admin.portfolios.index": [],
    "admin.portfolios.store": [],
    "admin.portfolios.create": [],
    "admin.portfolios.edit": [
        {
            "name": "portfolio",
            "binding": "hash_id"
        }
    ],
    "admin.portfolios.update": [
        {
            "name": "portfolio"
        }
    ],
    "admin.portfolios.delete": [
        {
            "name": "portfolio"
        }
    ],
    "admin.rate-cards.index": [],
    "admin.rate-cards.store": [],
    "admin.rate-cards.create": [],
    "admin.rate-cards.edit": [
        {
            "name": "rateCard"
        }
    ],
    "admin.rate-cards.update": [
        {
            "name": "rateCard"
        }
    ],
    "admin.rate-cards.delete": [
        {
            "name": "rateCard"
        }
    ],
    "admin.profiles.videos": [
        {
            "name": "profile",
            "binding": "slug"
        }
    ],
    "admin.profiles.videos.store": [
        {
            "name": "profile",
            "binding": "slug"
        }
    ],
    "admin.profiles.videos.destroy": [
        {
            "name": "video",
            "binding": "id"
        }
    ],
    "admin.profiles.rate-cards": [
        {
            "name": "profile",
            "binding": "hash_id"
        }
    ],
    "admin.profiles.rate-cards.store": [
        {
            "name": "profile"
        }
    ],
    "admin.profiles.rate-cards.destroy": [
        {
            "name": "portfolio"
        }
    ],
    "admin.profiles.rate-cards.update": [
        {
            "name": "portfolio"
        }
    ],
    "admin.profiles.rate-cards.edit": [
        {
            "name": "portfolio"
        }
    ],
    "admin.profiles.proposals": [
        {
            "name": "profile",
            "binding": "hash_id"
        }
    ],
    "admin.profiles.media": [
        {
            "name": "profile",
            "binding": "hash_id"
        }
    ],
    "admin.profiles.media.store": [
        {
            "name": "profile",
            "binding": "hash_id"
        }
    ],
    "admin.profiles.portfolios": [
        {
            "name": "profile",
            "binding": "hash_id"
        }
    ],
    "admin.speakers.index": [],
    "admin.speakers.search": [],
    "admin.speakers.create": [],
    "admin.speakers.show": [
        {
            "name": "speaker",
            "binding": "slug"
        }
    ],
    "admin.speakers.store": [],
    "admin.speakers.edit": [
        {
            "name": "speaker",
            "binding": "slug"
        }
    ],
    "admin.speakers.update": [
        {
            "name": "speaker"
        }
    ],
    "admin.speakers.delete": [
        {
            "name": "speaker"
        }
    ],
    "admin.speakers.videos": [
        {
            "name": "speakers"
        }
    ],
    "admin.speakers.videos.store": [
        {
            "name": "speakers"
        }
    ],
    "admin.speakers.videos.destroy": [
        {
            "name": "video",
            "binding": "id"
        }
    ],
    "admin.referrals.index": [],
    "admin.referrals.store": [],
    "admin.referrals.create": [],
    "admin.referrals.edit": [
        {
            "name": "referral"
        }
    ],
    "admin.referrals.update": [
        {
            "name": "referral"
        }
    ],
    "admin.referrals.delete": [
        {
            "name": "referral"
        }
    ],
    "admin.gallery.index": [],
    "admin.gallery.store": [],
    "admin.gallery.delete": [
        {
            "name": "gallery",
            "binding": "id"
        }
    ],
    "admin.blogs.index": [],
    "admin.blogs.store": [],
    "admin.blogs.create": [],
    "admin.blogs.edit": [
        {
            "name": "blog",
            "binding": "slug"
        }
    ],
    "admin.blogs.update": [
        {
            "name": "blog",
            "binding": "slug"
        }
    ],
    "admin.blogs.delete": [
        {
            "name": "blog",
            "binding": "slug"
        }
    ],
    "admin.settings.location": [],
    "admin.settings.location.store": [],
    "admin.deals.index": [],
    "admin.deals.store": [],
    "admin.deals.create": [],
    "admin.deals.edit": [
        {
            "name": "deal"
        }
    ],
    "admin.deals.update": [
        {
            "name": "deal"
        }
    ],
    "admin.deals.delete": [
        {
            "name": "deal"
        }
    ],
    "admin.proposals.index": [],
    "admin.proposals.store": [],
    "admin.proposals.create": [],
    "admin.proposals.show": [
        {
            "name": "proposal"
        }
    ],
    "admin.proposals.portfolios.suggest": [],
    "admin.proposals.edit": [
        {
            "name": "proposal"
        }
    ],
    "admin.proposals.update": [
        {
            "name": "proposal"
        }
    ],
    "admin.proposals.delete": [
        {
            "name": "proposal"
        }
    ],
    "register": [],
    "login": [],
    "password.request": [],
    "password.email": [],
    "password.reset": [
        {
            "name": "token"
        }
    ],
    "password.store": [],
    "verification.notice": [],
    "verification.verify": [
        {
            "name": "id"
        },
        {
            "name": "hash"
        }
    ],
    "verification.send": [],
    "password.confirm": [],
    "password.update": [],
    "logout": []
}
}
export {};
