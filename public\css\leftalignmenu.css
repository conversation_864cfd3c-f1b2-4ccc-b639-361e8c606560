#wrapper
{
	padding-top: 75px;
}

.top_bar .standard_wrapper
{
	height: 74px;
}

#logo_wrapper
{
	text-align: left;
	border: 0;
	padding: 0;
	float: left;
	width: 100%;
	height: 100%;
}

#logo_wrapper .logo_container
{
	display: table;
	padding: 0;
	height: 100%;
}

.top_bar.scroll #logo_wrapper
{
	display: block;
}

#nav_wrapper
{
	border-top: 0 !important;
}

.logo_container
{
	display: block;
	float: left;
	width: auto;
	height: auto;
}

html[data-menu=leftalign] #menu_wrapper
{
	width: auto;
	float: left;
	height: 100%;
	padding: 0;
	margin-left: 60px;
}

html[data-menu=leftalign] #logo_right_wrapper #logo_right_button
{
	position: relative;
	display: table-cell;
    vertical-align: middle;
    right: 0;
    top: 0;
}

html[data-menu=leftalign] #logo_right_wrapper
{
	width: auto;
	float: right;
	height: 100%;
	padding: 0;
	display: table;
}

/* 
#Tablet (Portrait)
================================================== 
*/

@media only screen and (min-width: 768px) and (max-width: 960px) {
	html[data-menu=leftalign] #logo_wrapper { padding: 0; }
	html[data-menu=leftalign] .logo_container { margin-top: 0px; }
}

/*  
#Mobile (Portrait)
================================================== 
*/

@media only screen and (max-width: 767px) {
	html[data-menu=leftalign] .top_bar .standard_wrapper
	{
		height: auto;
		padding: 0;
	}
	
	html[data-menu=leftalign] #nav_wrapper, html[data-menu=leftalign] #menu_wrapper
	{
		display: block;
	}
	
	html[data-menu=leftalign] .top_bar
	{
		padding-bottom: 0;
	}
}