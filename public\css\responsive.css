/**
*
* Widescreen Devices
*
**/

@media only screen and (min-width: 1200px) {
	
	#page_content_wrapper:not(.wide), .standard_wrapper, .footer_bar_wrapper, #footer ul.sidebar_widget, #page_content_wrapper.wide:not(.photoframe) .standard_wrapper, .ppb_popup.one.contain, #blog_promo_link_wrapper, #menu_wrapper, .above_top_bar .page_content_wrapper:not(.wide)
	{
		max-width: 1425px;
		width: 100%;
		box-sizing: border-box;
		margin: auto;
		padding: 0 90px;
	}
	
	#page_content_wrapper.blog_wrapper
	{
		padding: 0;
		width: calc(100% - 180px);
		margin-bottom: 60px;
		margin-top: 60px;
		max-width: 1245px;
	}
	
	#footer ul.sidebar_widget
	{
		padding-top: 60px;
		padding-bottom: 60px;
	}
	
	#page_content_wrapper .standard_wrapper
	{
		padding: 0;
	}
	
	#page_content_wrapper.wide.nomargin, .page_content_wrapper.wide.nomargin
	{
		margin-top: 0;
	}
	
	#page_content_wrapper.wide:not(.photoframe), .page_content_wrapper.wide:not(.photoframe), #page_content_wrapper.wide:not(.split), .page_content_wrapper.wide:not(.split)
	{
		width: 100%;
		max-width: 100%;
		padding: 0;
	}
	
	.footer_bar_wrapper
	{
		padding-top: 30px;
		padding-bottom: 20px;
	}
	
	#page_content_wrapper.fullwidth, .page_content_wrapper.fullwidth
	{
		width: 100%;
		padding: 0;
		max-width: 100%;
	}
	
	.mobile_menu_wrapper .page_content_wrapper
	{
		padding: 0 !important;
	}
	
	#logo_wrapper .social_wrapper
	{
		left: 90px;
	}
	
	#logo_right_button
	{
		right: 90px;
	}
}


/* 
#Tablet (Landscape)
================================================== 
*/

@media only screen and (min-width: 960px) and (max-width: 1024px)
{	
	body
	{
		line-height: 1.6;
	}
	
	#page_content_wrapper:not(.wide), .page_content_wrapper:not(.wide)
	{
		width: calc(100% - 90px);
	}
	
	#page_caption.hasbg
	{
		max-height: 450px;
	}
	
	blockquote
	{
		font-size: 22px;
	}
	
	#option_btn
	{
		top: 100px;
	}
	
	.mobile_main_nav, #sub_menu
	{
		margin: 0;
		padding: 0;
	}
	
	.mobile_menu_wrapper
	{
		padding: 60px;
	}
	
	body.js_nav #wrapper
	{
		transform: translateZ(0px) translateX(300px) rotateY(0deg);
	}
	
	body.js_nav #wrapper
	{
		left: 90px;
	}
	
	#page_caption h1
	{
		font-size: 42px;
	}
	
	.post_header.grid h6
	{
		font-size: 20px;
	}
	
	.team_grid_desc h5
	{
		font-size: 18px;
	}
	
	.gallery_archive_info_content h1
	{
		font-size: 30px;
	}
	
	.post_caption h1
	{
		font-size: 30px;
	}
	
	.post_header h5
	{
		font-size: 24px;
	}
	
	.social_share_button_wrapper ul
	{
		border: 0;
		padding-left: 0;
	}
	
	.portfolio_post_wrapper
	{
		padding: 0 30px 0 30px;
		box-sizing: border-box;
	}
	
	.standard_wrapper
	{
		box-sizing: border-box;
	}
	
	.parallax:not(.inner_box)
	{
		z-index: 0;
	}
	
	body.single-post .post_content_wrapper h1
	{
		font-size: 24px;
	}
	
	body.elementor-fullscreen #perspective
	{
		overflow: hidden;
	}
	
	#menu_border_wrapper { height: auto; display: none; }
	
	#mobile_nav_icon
	{
		display: inline-block !important;
	}
	
	#wrapper
	{
		transition: transform 0.2s;
	}
	
	body.elementor-fullscreen:not(.elementor-overflow) #perspective
	{
		overflow: hidden;
	}
	
	#page_caption.hasbg .page_title_wrapper .page_title_inner .page_title_content
	{
		width: 100%;
	}
	
	#searchform input[type=text] { width: 50%; }
	
	#option_btn, #option_wrapper
	{
		display: none;
	}
	
	body.tg_footer_reveal #footer_wrapper
	{
		position: relative;
	}
	
	#learn-press-course
	{
		width: 100%;
	}
	
	.standard_wrapper
	{
		width: 100%;
	}
	
	body.learnpress-page.profile #learn-press-profile-content
	{
		width: calc(100% - 240px);
	}
	
	body.single.single-post #page_caption h1
	{
		font-size: 40px;
	}
	
	.header_style_wrapper .top_bar
	{
		padding: 0 20px 0 20px;
	}
}


/* 
#Tablet (Portrait)
================================================== 
*/

@media only screen and (min-width: 768px) and (max-width: 960px) {
	body { font-size: 13px; overflow-x: hidden; }
	
	.mobile_main_nav, #sub_menu
	{
		margin: 0;
		padding: 0;
	}
	
	.mobile_menu_wrapper
	{
		padding: 50px;
	}
	
	body.js_nav #wrapper
	{
		left: 0px;
	}
	
	body.js_nav #wrapper
	{
		transform: translateZ(0px) translateX(400px) rotateY(0deg);
	}
	
	#wrapper { padding-top: 75px; }
	.top_bar { width: 100%; padding: 0; }
	#logo_wrapper { padding: 20px 0 20px 0; }
	.footer_before_widget .footer_logo.logo_wrapper img { max-height: none; margin-top: 0; margin: auto; }
	html[data-style=fullscreen] #logo_wrapper, .top_bar.hasbg #logo_wrapper { border: 0; }
	#logo_wrapper { border: 0; }
	body.leftmenu .header_style_wrapper { display: block; }

	.standard_wrapper { width: 100%; box-sizing: border-box; }
	.page_content_wrapper .inner { width: 100%; padding: 0; box-sizing: border-box; }
	h1 { font-size: 30px; }
	h2 { font-size: 22px; }
	h3 { font-size: 20px; }
	h4 { font-size: 18px; }
	h5 { font-size: 16px; }
	h6 { font-size: 14px; }
	h7 { font-size: 12px; }
	#page_caption h1 { font-size: 26px; }
	
	#page_content_wrapper .inner .sidebar_content.full_width, .gallery_mansory_wrapper { width: 100%; box-sizing: border-box; padding: 0; }
	#page_content_wrapper .inner .sidebar_content.full_width img, .page_content_wrapper img { max-width: 100%; height: auto; }
	#page_content_wrapper .inner .sidebar_content.full_width#blog_grid_wrapper, #page_content_wrapper .inner .sidebar_content.full_width#galleries_grid_wrapper { width: 00%; }
	#menu_wrapper { width: 100%; }
	#page_content_wrapper, .page_content_wrapper, #page_caption .page_title_wrapper { width: 100%; }
	.footer_bar { width: 100%; padding-bottom: 0; }
	#menu_border_wrapper { height: auto; display: none; }
	.portfolio_header h6 { width: 89%; }
	#page_content_wrapper .inner .sidebar_content, .page_content_wrapper .inner .sidebar_content { width: 68%; border: 0; }

	#page_content_wrapper .inner .sidebar_content.full_width.portfolio4_content_wrapper { width: 760px; }
	.portfolio_header h6 { font-size: 16px; }
	.one_fourth.portfolio4 { height: 320px; }
	.post_img.animate div.thumb_content i { font-size: 2em; margin: 0 5px 0 5px; }
	#page_content_wrapper .inner .sidebar_wrapper { width: 220px; margin: 0; }
	#page_content_wrapper .inner .sidebar_wrapper .sidebar .content { margin: 0; }
	#page_content_wrapper .inner .sidebar_wrapper .sidebar { width: 100%; }
	.social_wrapper ul li img { width: 22px; }
	div.home_header { font-size: 70px; letter-spacing: -2px; line-height: 80px; margin-left: 40px; margin-right: 40px; }

	#page_content_wrapper .inner { width: 100%; } 
	#page_content_wrapper .sidebar .content .posts.blog li img, #page_content_wrapper .posts.blog li img { width: 50px; height: auto; }
	#footer { width: 100%; box-sizing: border-box; }
	#footer ul.sidebar_widget { width: 100%; margin: auto; padding: 40px 30px 40px 30px; }
	#page_content_wrapper:not(.wide), .page_content_wrapper:not(.wide) { width: 100%; margin-top: 0; padding: 0 30px 0 30px; box-sizing: border-box; }
	#page_content_wrapper.wide, .mobile_menu_wrapper .mobile_menu_content > div { padding: 0; }
	#page_content_wrapper .sidebar .content .posts.blog { float: left; margin-bottom: 20px; width: 100%; }
	.comment { width: 720px; }
	.comment .right { width: 82%; }
	ul.children div.comment .right { width: 78%; }
	#content_wrapper ul.children ul.children { width: 77%; }
	#menu_wrapper .nav ul, #menu_wrapper div .nav { display: none; }

	.social_wrapper { float: none; margin: auto; width: auto; }
	.footer_bar_wrapper .social_wrapper { margin-right: 10px; }
	.above_top_bar .social_wrapper ul { margin-top: 0; }

	#page_content_wrapper .inner .sidebar_content.full_width#blog_grid_wrapper { width: 100%; }
	#page_content_wrapper .inner .sidebar_content.full_width .post_wrapper.grid_layout .post_img img { width: 100% !important; height: auto !important; }
	
	#page_content_wrapper .inner .sidebar_content { margin-right: 0; margin-top: 0; padding-right: 10px; padding-top: 0; }
	#page_content_wrapper .inner .sidebar_content.left_sidebar { margin-right: 0; padding-top: 0; padding-left: 30px; }
	#page_content_wrapper .inner .sidebar_wrapper, #page_content_wrapper .inner .sidebar_wrapper.left_sidebar { width: 28%; margin-right: 0; padding-left: 10px; padding-top: 0; margin-right: 0; }
	.page_content_wrapper .inner .sidebar_wrapper { width: 30%; }
	#page_content_wrapper .inner .sidebar_wrapper.left_sidebar { padding-left: 0; margin-right: 0; }
	#page_content_wrapper .inner .sidebar_content.full_width .post_wrapper .post_img img, #page_content_wrapper .inner .sidebar_content .post_wrapper .post_img img { max-width: 100%; height: auto; }

	.post_content_wrapper { width: 100%; }

	.comment .right { width: 54%; }
	ul.children div.comment .right { width: 62%; }
	#page_content_wrapper .sidebar .content .sidebar_widget li ul.flickr li img { width: 62px; height: auto; }
	.page_control { left: 46%; }	
	#footer ul.sidebar_widget li ul.posts.blog li img { width: 50px; }
	.post_circle_thumb { width: 60px; height: 60px; margin-right: 15px; }
	.testimonial_slider_wrapper { font-size: 16px; }
	
	#footer .sidebar_widget.four > li { width: 46%; margin-right: 3%; margin-bottom: 3%; }
	#footer .sidebar_widget.four > li:nth-child(2), #footer .sidebar_widget.four > li:nth-child(4) { width: 46%; margin-right: 0; float: right; }
	#footer .sidebar_widget.four > li:nth-child(3) { clear: both; }
	.portfolio_desc.team h5 { font-size: 20px; }
	.portfolio_desc h6, .portfolio_desc h5 { font-size: 18px; }

	.slider_wrapper .flexslider img { max-width: 100% !important; height: auto; }
	
	h1.product_title { font-size: 26px; }
	p.price ins span.amount, p.price span.amount { font-size: 20px; }
	.post_img.small img.thumbnail_gallery { max-width: 143px !important; }
	
	.team_wrapper .one_half, .team_wrapper .one_half.last, .testimonial_wrapper .one_half, .testimonial_wrapper .one_half.last { width: 100%; margin-right: 0; margin-bottom: 4%; clear: both; }
	
	.one.withbg, .one.withbg[data-mouse="mouse_pos"], .one.withbg[data-type="background"] { background-attachment: scroll !important; background-size: cover !important; background-position: center top !important; }
	#page_caption.hasbg h1.withtopbar, h1.hasbg.withtopbar, #page_caption.hasbg.notransparent h1.withtopbar { margin-top: 160px !important; }

	.one_half_bg.nopadding { padding: 0 !important; }
	.one_half_bg, .one_third_bg, .two_third_bg { padding: 0; }
	a.button.fullwidth.ppb .title { font-size: 26px; }
	#copyright { float: none; width: 100%; margin-bottom: 0; text-align: center; clear: both; margin-top: 10px; }
	.menu-footer-menu-container { width: 100%; text-align: center; }
	#footer_menu { float: none; width: 100%; text-align: center; }
	#footer_menu li { float: none; display: inline-block; }
	input[type=submit].medium, input[type=button].medium, a.button.medium { padding: .3em 1.2em .3em 1.2em; }
	.contact_form_wrapper input[type=text], .contact_form_wrapper input[type=email], .contact_form_wrapper input[type=date] { margin-bottom: 4%; }
	.one_half_bg.contact_form { padding: 40px; }
	#page_content_wrapper .inner .sidebar_content, .page_content_wrapper .inner .sidebar_content { margin-right: 0; }
	#page_content_wrapper .inner .sidebar_content.left_sidebar { border: 0; padding-left: 0; padding-right: 0; }
	.ppb_content { width: 100%; }
	.page_content_wrapper.fullwidth .portfolio_filter_dropdown, #page_content_wrapper.fullwidth .portfolio_filter_dropdown { margin-right: 3%; }
	#page_content_wrapper .inner #portfolio_filter_wrapper.sidebar_content { width: 100%; padding: 0; }
	.sidebar_content.three_cols.gallery .element { width: 100%; margin-right: 0; }

	#page_content_wrapper .inner #blog_grid_wrapper.sidebar_content { padding: 0; }
	#page_content_wrapper .inner #blog_grid_wrapper.sidebar_content.left_sidebar { padding-left: 0; }
	.post_header.grid h6 { font-size: 18px; }
	#page_content_wrapper .posts.blog li, .page_content_wrapper .posts.blog li { width: 100%; float: none; margin-right: 0; }
	#page_content_wrapper .inner .sidebar_content #commentform p.comment-form-author, #page_content_wrapper .inner .sidebar_content #commentform p.comment-form-email, #page_content_wrapper .inner .sidebar_content #commentform p.comment-form-url { width: 27.6%; }

	.post_img.team { border-radius: 0; }
	.post_header h5, .post_quote_title { font-size: 24px; }
	#autocomplete { margin-top: 11px; }
	.post_header.search { width: 80%; }
	#searchform input[type=text] { width: 50%; }
  	.one_half .page_content_wrapper .inner, .one_third .page_content_wrapper .inner, .one_fourth .page_content_wrapper .inner, .one_fifth .page_content_wrapper .inner, .two_third .page_content_wrapper .inner
	{
	    max-width: 100%;
	    width: 100%;
	    padding: 0;
	}
	
	#page_caption h1
	{
		font-size: 24px;
	}
	.one_half_bg { padding: 40px !important; }
	
	.one_third_bg.team_photo { width: 33.3%; }
	.two_third_bg.team { width: 66.6%; padding: 20px; }
	#page_content_wrapper .inner .sidebar_content #commentform p.comment-form-author, #page_content_wrapper .inner .sidebar_content #commentform p.comment-form-email, #page_content_wrapper .inner .sidebar_content #commentform p.comment-form-url { width: 100%; box-sizing: border-box; }
		
	.three_cols.gallery .element .portfolio_title h5
	{
		font-size: 16px;
	}
	
	.three_cols.gallery .element .portfolio_title .post_detail
	{
		font-size: 12px;
	}
	
	#page_content_wrapper.split #portfolio_filter_wrapper.two_cols.gallery .element
	{
		width: 100%;
		margin-right: 0;
		margin-bottom: 20px;
	}
	
	#page_content_wrapper.split #portfolio_filter_wrapper.two_cols.gallery.wide .element
	{
		margin-bottom: 0;
	}
	
	.portfolio_desc.portfolio4 .post_detail
	{
		display: none;
	}
	
	.top_bar.scroll #logo_wrapper
	{
		display: block;
	}
	
	.woocommerce #content .quantity input.qty, .woocommerce .quantity input.qty, .woocommerce-page #content .quantity input.qty, .woocommerce-page .quantity input.qty
	{
		height: 30px;
	}
	
	#mobile_nav_icon
	{
		display: inline-block !important;
	}
	
	.image_classic_frame
	{
		margin-bottom: 20px;
	}
	
	.one_third.gallery3 .gallery_archive_desc h4, .one_fourth.gallery4 .gallery_archive_desc h4
	{
		font-size: 14px;
	}
	
	.standard_wrapper
	{
		width: 100%;
	}
	
	.sidebar_content:not(.full_width) .standard_wrapper
	{
		padding: 0;
	}
	
	.post_caption h1
	{
		font-size: 24px;
	}
	
	.four_cols.gallery .element .portfolio_title h5
	{
		font-size: 14px;
	}
	
	.post_img.fadeIn, .post_img.grid.fadeIn
	{
		opacity: 1;
	}
	
	.fullwidth_comment_wrapper .comment .right
	{
		width: 78%;
	}
	
	#nav_wrapper
	{
		border: 0;
	}
	
	.three_cols.gallery.wide .element.double_size
	{
		width: 66.65%;
	}
	
	#menu_wrapper .nav ul li a, #menu_wrapper div .nav li > a, html[data-menu=centeralogo] body #logo_right_button
	{
		padding-top: 0;
	}
	
	.social_share_button_wrapper ul
	{
		border: 0;
		padding-left: 0;
	}
	
	.parallax
	{
		z-index: 0;
	}
	
	.portfolio_post_wrapper
	{
		margin-bottom: 0;
	}
	
	body.single-portfolios .portfolio_post_wrapper
	{
		margin-bottom: 30px;
	}
	
	#toTop
	{
		bottom: 15px;
	}
	
	#logo_normal.logo_container
	{
		left: 0;
	}
	
	.footer_bar_wrapper
	{
		width: 100%;
		padding-top: 40px;
		padding-bottom: 20px;
	}
	
	.above_top_bar .page_content_wrapper
	{
		width: 100%;
		padding: 0 30px 0 30px;
		box-sizing: border-box;
	}
	
	body #page_caption h1
	{
		font-size: 32px;
	}
	
	#page_caption.hasbg
	{
		height: 450px;
	}
	
	blockquote
	{
		font-size: 20px;
	}
	
	.woocommerce ul.products li.product, .woocommerce-page ul.products li.product
	{
		width: 48.5%;
	}
	
	.woocommerce ul.products li.product:nth-child(2n), .woocommerce-page ul.products li.product:nth-child(2n), .woocommerce-page[class*=columns-] ul.products li.product:nth-child(2n), .woocommerce[class*=columns-] ul.products li.product:nth-child(2n)
	{
		margin-right: 0 !important;
	}
	
	.woocommerce #content div.product div.images, .woocommerce #content div.product div.summary, .woocommerce div.product div.images, .woocommerce div.product div.summary, .woocommerce-page #content div.product div.images, .woocommerce-page #content div.product div.summary, .woocommerce-page div.product div.images, .woocommerce-page div.product div.summary
	{
		width: 50%;
		padding: 0 10px 0 10px;
		box-sizing: border-box;
		float: left;
	}
		
	body.admin-bar #close_mobile_menu
	{
		top: 46px;
	}
	
	body.admin-bar .header_style_wrapper
	{
		padding-top: 46px;
	}
	
	#page_content_wrapper .posts.blog li a, .page_content_wrapper .posts.blog li a, #footer ul.sidebar_widget li ul.posts.blog li a
	{
		letter-spacing: 0;
	}
	
	#page_content_wrapper .inner .sidebar_content.full_width.blog_f
	{
		width: 100%;
	}
	
	.woocommerce ul.products li.product, .woocommerce-page ul.products li.product, .woocommerce ul.products.columns-3 li.product, .woocommerce-page ul.products.columns-3 li.product
	{
		width: 48%;
	}
	
	.woocommerce div.product .woocommerce-tabs ul.tabs li a, .woocommerce-page div.product .woocommerce-tabs ul.tabs li a
	{
		padding: 10px 20px 9px 20px !important;
	}
	
	#page_content_wrapper.blog_wrapper.hasbg
	{
		padding-top: 30px;
		padding-bottom: 30px;
	}
	
	body.centeralign .logo_container .logo_wrapper:not(.hidden)
	{
		margin-top: 0;
	}
	
	body.leftmenu .mobile_menu_wrapper
	{
		left: -10px;
	    -webkit-transition: -webkit-transform 200ms ease;
	    -moz-transition: transform 200ms ease;
	    -o-transition: -o-transform 200ms ease;
	    transition: transform 200ms ease;
	    -webkit-transform: translate(-400px, 0px);
	    -moz-transform: translate(-400px, 0px);
	    transform: translate(-400px, 0px);
	}
	
	body.leftmenu #wrapper
	{
		width: 100%;
		transform-origin: 50% 50% 50%;
		left: 0;
		padding-top: initial !important;
	}
	
	body.leftmenu .tg_fullscreen_gallery_wrapper,
	body.leftmenu .tg_parallax_slide_container,
	body.leftmenu .tg_parallax_slide_background,
	body.leftmenu .tg_animated_frame_slider_wrapper.slideshow,
	body.leftmenu .tg_room_slider_wrapper,
	body.leftmenu .tg_popout_slide_container.slider,
	body.leftmenu .tg_transitions_slide_container
	{
		width: 100%;
		left: 0;
	}
	
	body.leftmenu .icon-scroll {
		left: 50%;
	}
	
	body.leftmenu .tg_room_slider_wrapper .slide
	{
		width: 50%;
		margin: 7vh 0 0 20vw;
	}
	
	body.leftmenu .tg_skewed_slide_container .slider-page__content
	{
		 padding: 0 32% 0 32%;
	}
	
	body.leftmenu .tg_clip_path_slide_container .slide__content
	{
		left: calc(5%);
		bottom: 30px;
	}
	
	body.leftmenu #page_content_wrapper .inner .sidebar_content .tg_split_slick_slide_container .slideshow .slider .item img
	{
		left: 50%;
		min-width: 101%;
	}
	
	body.leftmenu .tg_split_slick_slide_container .slideshow-text 
	{
		left: 50%;
	}
	
	body.tg_footer_reveal #footer_wrapper
	{
		position: relative;
	}
	
	#wrapper
	{
		transition: transform 0.2s;
	}
	
	#page_caption.hasbg .page_title_wrapper .page_title_inner .page_title_content
	{
		width: 100%;
	}
	
	#option_btn, #option_wrapper
	{
		display: none;
	}
	
	#learn-press-course 
	{
	    width: 100%;
	}
	
	.single_course_title
	{
		width: 60%;
	}
	
	.single_course_join
	{
		width: 40%;
	}
	
	body .course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta
	{
		display: none;
	}
	
	body.learnpress-page.checkout .learn-press-checkout-comment,
	body.learnpress-page.checkout  #learn-press-payment,
	body.learnpress-page.checkout #learn-press-order-review
	{
		width: 100%;
		float: none;
		clear: both;
	}
	
	body.learnpress-page.checkout #learn-press-order-review
	{
		margin-bottom: 20px;
	}
	
	body.learnpress-page.profile #learn-press-profile-content
	{
		width: calc(100% - 230px);
	}
	
	#page_content_wrapper ul.learn-press-nav-tabs .course-nav a, body.learnpress-page.profile .lp-tab-sections li a, body.learnpress-page.profile .lp-tab-sections li span, body.learnpress-page.profile .lp-tab-sections .section-tab.active span
	{
		padding: 5px 25px 5px 25px;
	}
	
	body.single.single-post #page_caption h1
	{
		font-size: 40px;
	}
	
	.header_style_wrapper .top_bar
	{
		padding: 0 20px 0 20px;
	}
	
	body.single.single-post #page_content_wrapper.blog_wrapper .page_title_content h1
	{
		font-size: 50px;
		width: 100%;
	}
	
	#single_course_header
	{
		padding-left: 20px;
		padding-right: 20px;
	}
	
	body.single.single-lp_course #lp-single-course .single_course_title h1, 
	body.single.single-meeting .single_course_title h1
	{
		font-size: 36px;
		line-height: 1.3;
	}
}

/*  
#Mobile (Portrait)
================================================== 
*/

@media only screen and (max-width: 767px) {
	body { -webkit-text-size-adjust: none; }
	body { font-size: 13px; }
	.logo_container { display: block; }
	.logo_wrapper img { max-width: 250px; height: auto; margin-top: 0px; display: inline-block; }
	.footer_before_widget .footer_logo.logo_wrapper img { max-height: none; margin-top: 0; margin: auto; }
	
	.top_bar { width: 100%; padding: 0 30px 15px 30px; }
	body.elementor-page .top_bar { padding: 0 20px 15px 20px; }
	.top_bar { padding-bottom: 0 !important; }
	#top_contact_hours, #top_contact_number { display: none; }
	.mobile_menu_wrapper { width: 270px; }

	.standard_wrapper { width: 100%; padding: 0 30px 0 30px; box-sizing: border-box; }
	h1 { font-size: 30px; }
	h2 { font-size: 28px; }
	h3 { font-size: 26px; }
	h4 { font-size: 24px; }
	h5 { font-size: 20px; }
	h6 { font-size: 18px; }
	h7 { font-size: 17px; }
	.post_header.grid h6, .post_quote_title.grid { font-size: 18px; }

	#page_caption h1 { font-size: 28px !important; }
	#page_caption .page_title_wrapper { width: 100%; margin: auto; }
	#page_caption { padding-left: 30px; padding-right: 30px; padding-top: 40px; padding-bottom: 40px; box-sizing: border-box; margin-bottom: 30px; float: left; }
	
	#page_content_wrapper .inner .inner_wrapper { padding: 0; }
	#page_content_wrapper .inner .sidebar_content.full_width { width: 100%; margin-top: 15px; margin-bottom: 0; }
	#page_content_wrapper .inner .sidebar_content img { max-width: 100%; height: auto; }
	body #page_content_wrapper:not(.wide), body .page_content_wrapper:not(.wide) { width: 100%; margin-top: 0; padding: 0 30px 0 30px; box-sizing: border-box; }
	#page_content_wrapper.wide, .page_content_wrapper.wide { padding: 0; }
	#page_content_wrapper .inner { width: 100%; padding: 0; }
	#page_content_wrapper .inner #page_main_content { margin-top: 0; padding-bottom: 0; margin-bottom: 0; }
	#page_content_wrapper .inner { width: 100%; margin: auto; }
	#page_content_wrapper .inner .sidebar_content { width: 100%; margin-top: 0; padding-bottom: 0; float: none; border: 0 !important; }
	#page_content_wrapper .inner .sidebar_content.portfolio, #page_content_wrapper .inner .sidebar_content.portfolio #portfolio_filter_wrapper { padding-top: 0; }
	#page_content_wrapper .inner .sidebar_content.full_width { width: 100%; margin-top: 0; }
	#page_content_wrapper .inner #page_caption.sidebar_content.full_width { padding-bottom: 20px; }
	#page_content_wrapper .inner .sidebar_content.full_width img { max-width: 100%; height: auto; }
	#page_content_wrapper .inner .sidebar_wrapper { width: 100%; margin-left: 0; margin-bottom: 0; margin-top: 25px; padding-top: 0; }
	#page_content_wrapper .inner .sidebar_wrapper .sidebar .content { margin: 0; }
	#page_content_wrapper .inner .sidebar_content.full_width#blog_grid_wrapper, #page_content_wrapper .inner .sidebar_content.full_width#galleries_grid_wrapper { width: 100%; }
	.portfolio_header h6 { width: 31%; font-size: 12px; }
	#page_content_wrapper .inner .sidebar_content img { max-width: 100%; height: auto; }
	.fullwidth_comment_wrapper .comment .right { width: 66%; margin-left: 15px; }
	.fullwidth_comment_wrapper ul.children div.comment .right { width: 64%; padding: 0; }
	
	ul.children div.comment ul.children div.comment .right { width: 50%; }
	
	#content_wrapper ul.children ul.children { width: 100%; }
	ul.children .comment { margin-left: 0; }
	#content_wrapper ul.children { border: 0; }
	#content_wrapper ul.children ul.children { margin-left: -16px; }
	
	.comment_wrapper #respond { margin-left: 20px; }
	.comment_wrapper ul.children #respond { margin-left: -55px; }
	#menu_wrapper { width: 100%; }
	#menu_border_wrapper, #menu_border_wrapper_right { height: auto; display: none; }
	
	#portfolio_filter_wrapper.two_cols { width: 100%; }
	#portfolio_filter_wrapper.two_cols .element { width: 100%; }
	.one_half.gallery2, .one_half.portfolio2, .one_half.gallery2 .mask, .one_half.gallery2.filterable { width: 100%; height: auto; }
	.one_half.gallery2 .mask { width: 100%; height: 100% !important; }
	.one_half.portfolio2 { margin-bottom: 5%; margin-right: 5%; }
	.one_half.gallery2 .mask .button.circle { margin-top: 30%; }
	.one_half.gallery2 img.blur_effect, .one_half.portfolio2 img.blur_effect { width: 100%; height: auto; }
	.one_half.gallery2:hover .mask .mask_circle { width: 32px; height: 32px; margin-top: 22%; }
	.one_half.gallery2 .mask .mask_circle i { font-size: 30px; line-height: 34px; }
	
	#portfolio_filter_wrapper.three_cols { width: 100%; }
	#portfolio_filter_wrapper.three_cols .element { width: 100%; float: left; margin-right: 0%;}
	.one_third.gallery3, .one_third.portfolio3, .one_third.gallery3 .mask, .one_third.gallery3.filterable { width: 100%; height: auto; }
	.one_third.gallery3 .button.circle img { width: 30px !important; height: 30px !important; }
	.one_third.gallery3 .mask { width: 100%; height: 100% !important; }
	.one_third.gallery3 img.blur_effect, .one_third.portfolio3 img.blur_effect { width: 100%; height: auto; }
	.one_third.gallery3 h5 { font-size: 14px !important; }
	.one_third.gallery3:hover .mask .mask_circle { width: 32px; height: 32px; margin-top: 22%; }
	.one_third.gallery3 .mask .mask_circle i { font-size: 30px; line-height: 34px; }
	
	#portfolio_filter_wrapper.four_cols { width: 100%; }
	#portfolio_filter_wrapper.four_cols .element { width: 100%; margin-right: 0;}
	.one_fourth, .one_fourth.last { width: 100%; margin-right: 0; margin-bottom: 2%; clear: both; }
	.one_fourth.gallery4, .one_fourth.portfolio4, .one_fourth.gallery4 .mask, .one_fourth.gallery4.filterable { width: 100%; height: auto; }
	.one_fourth.gallery4 img.blur_effect, .one_fourth.portfolio4 img.blur_effect { width: 100%; height: auto; }
	.one_fourth.gallery4 .mask { width: 100%; height: 100% !important; }
	.one_fourth.gallery4.portfolio_type h6 { margin-top: 34%; }
	.one_fourth.gallery4:hover .mask .mask_circle { width: 32px; height: 32px; margin-top: 22%; }
	.one_fourth.gallery4 .mask .mask_circle i { font-size: 30px; line-height: 34px; }
	.one_third.portfolio3_wrapper, .one_fourth.portfolio4_wrapper { width: 100%; clear: both; }
	
	.post_header { width: 100%; }
	.post_header h5 { font-size: 20px; letter-spacing: -1px; }
	blockquote { width: 100%; font-size: 18px; }
	blockquote:before { font-size: 2.0em; }
	#respond { margin-bottom: 0; width: 100%; }
	.post_wrapper { width: 100%;; margin-top: 0; }
	#page_content_wrapper .inner .sidebar_wrapper .sidebar { width: 100%; }

	#footer { width: 100%; }
	#footer ul.sidebar_widget { margin-left: 10px; }
	#footer ul.sidebar_widget { width: 100%; float: none; margin: auto; padding: 0 30px 0 30px; box-sizing: border-box; }
	#footer .sidebar_widget.four > li, #footer .sidebar_widget.three > li, #footer .sidebar_widget.two > li, #footer .sidebar_widget.one > li, #footer .sidebar_widget.four > li:nth-child(4), #footer .sidebar_widget.three > li:nth-child(3), #footer .sidebar_widget.two > li:nth-child(2) { width: 100%; }
	.footer_bar { margin-bottom: 0; }
	#copyright { width: 100%; }
	.social_wrapper, .social_wrapper.shortcode { float: none; margin: auto; width: auto; }
	.social_wrapper ul { margin-top: 15px; }
	.social_wrapper ul, .social_wrapper ul li { display: inline-table; }
	.footer_bar_wrapper { width: 100%; }
	#copyright { float: left; width: 100%; padding: 0 30px 0 30px; box-sizing: border-box; }
	.one_third, .one_third.last { width: 100%; margin-bottom: 3%; margin-right: 0; }
	.two_third, .two_third.last { width: 100%; margin-bottom: 3%; }
	
	#footer .sidebar_widget.four > li { margin-bottom: 20px; }
	.post_content_wrapper { width: 100%; }
	ul.children div.comment .right { width: 86%; }
	.page_control { left: 39%; bottom: 65px; }
	.one.fullwidth .page_content_wrapper { padding: 0; }
	.page_content_wrapper.nopadding { padding: 0 !important; }
	#page_content_wrapper .inner .sidebar_content.full_width.portfolio4_content_wrapper, .page_content_wrapper.portfolio4_content_wrapper { width: 100% !important; }
	
	.one_fourth, .one_fourth.last { width: 100%; margin-right: 0; }

	.post_img img { max-width: 100% !important; height: auto; }
	body.page-template-blog-g-php .post.type-post, body.error404 .post.type-post, body.search .hentry, body.page-template-galleries-php .galleries.type-galleries, body.tax-gallerycat .galleries.type-galleries, .ppb_blog_posts .post.type-post { width: 100%; }
	#blog_grid_wrapper .post.type-post { width: 100% !important; margin-left: 0; }
	#page_content_wrapper .inner .sidebar_wrapper.left_sidebar { width: 100%; padding-top: 0; margin-right: 0; margin-top: 30px; margin-bottom: 25px; }
	.post_header.full, .post_excerpt_full { width: 96%; }
	#page_content_wrapper .inner .sidebar_content.full_width#blog_grid_wrapper { width: 100%; }
	
	#blog_grid_wrapper { width: 100%; }
	.one_half, .one_half.last { width: 100%; clear: both; margin-bottom: 20px; }
	#blog_grid_wrapper { padding-top: 20px; }
	
	.post_img { margin-bottom: 20px; }
	#social_share_wrapper, .social_share_wrapper { margin-top: 30px; }
	#page_content_wrapper .posts.blog li { width: 100%; }
	body.single .post.type-post { float: none; padding-top: 30px; }
	.post_header h6 { font-size: 16px; }

	.alert_box_msg { max-width: 80% !important; }
	.social_wrapper.shortcode ul li { margin: 5px; }
	.one_fifth { width: 100%; float: none; }

	.footer_bar_wrapper .social_wrapper ul { margin-top: 0; }
	.above_top_bar .page_content_wrapper { margin: 0; margin: auto; }
	.above_top_bar .top_contact_info { width: 100%; float: none; margin: auto; text-align: center; border: 0; }
	.above_top_bar .top_contact_info span { border: 0; }
	.above_top_bar .social_wrapper { display: none; }

	.one.withsmallpadding:not(.withbg):not(.fullwidth), .one.withpadding:not(.withbg):not(.fullwidth) { padding: 30px 0 30px 0 !important; box-sizing: border-box; clear: both; margin: 0 !important; }
	.one.withpadding:not(.withbg):not(.fullwidth).parallax_content { padding: 30px 0 30px 0 !important; box-sizing: border-box; }

	.footer_bar_wrapper .social_wrapper { width: 100%; text-align: center; float: none; margin: 0 0 10px 0; }
	.footer_bar_wrapper .social_wrapper ul li { float: none; display: inline-block; margin: 0 5px 0 5px; }
	.comment .right { width: 100%; margin-left: 0; padding: 0 0 15px 0; }

	body.search .post_header { width: 72%; }
	
	h1.product_title { font-size: 28px; }

	.post_img.small img.thumbnail_gallery { max-width: 140px !important; }
	.one.withbg { background-attachment: scroll !important; background-size: cover !important; background-position: center top !important; background: #000; }
	
	.three_cols.gallery .element, .four_cols.gallery .element, #photo_wall_wrapper .wall_entry, .photo_wall_wrapper .wall_entry, #photo_wall_wrapper .wall_entry.three_cols, #photo_wall_wrapper .wall_entry.four_cols, .photo_wall_wrapper .wall_entry.four_cols { width: 100%; margin-right: 0%; }
	.page_content_wrapper .inner, .standard_wrapper { width: 100%; }
	
	.one_half_bg, .one_third_bg, .two_third_bg, .one_fourth_bg, .one_fifth_bg { width: 100%; padding: 30px !important; }
	.one_half_bg.nopadding, .two_third_bg > div, .one_half_bg > div, .one_third_bg > div, .one_fourth_bg > div { padding: 0 !important; }
	.one .page_content_wrapper.withbg { max-height: 300px; }

	#page_content_wrapper .inner .sidebar_content, .page_content_wrapper .inner .sidebar_content, #page_content_wrapper .inner .sidebar_content.portfolio { width: 100%; margin-right: 0; border: 0; margin-bottom: 30px; float: left; padding-right: 0; }
	#page_content_wrapper .inner .sidebar_content.left_sidebar, .page_content_wrapper .inner .sidebar_content.left_sidebar { width: 100%; float: none; padding: 0; clear: both; }
	#page_content_wrapper .inner .sidebar_wrapper, .page_content_wrapper .inner .sidebar_wrapper, #page_content_wrapper .inner .sidebar_wrapper.left_sidebar, .page_content_wrapper .inner .sidebar_wrapper.left_sidebar { width: 100%; float: left; margin-top: 20px; clear: both; }
	
	#page_content_wrapper .inner .sidebar_wrapper.left_sidebar, .page_content_wrapper .inner .sidebar_wrapper.left_sidebar
	{
		margin-top: 0;
	}

	.page_content_wrapper.fullwidth #portfolio_filter_wrapper.gallery, #page_content_wrapper.fullwidth #portfolio_filter_wrapper.gallery, .page_content_wrapper.fullwidth .portfolio_filter_wrapper.gallery, #page_content_wrapper.fullwidth .portfolio_filter_wrapper.gallery { width: 100%; margin: 0; }
	#page_content_wrapper .inner #portfolio_filter_wrapper.sidebar_content { width: 100%; }
	.page_content_wrapper .inner > div:not(.line_divider_border):not(.line_divider_seperator) { width: 100% !important; box-sizing: border-box; }
	#page_content_wrapper .inner #blog_grid_wrapper.sidebar_content, #blog_grid_wrapper.sidebar_content:not(.full_width) .post.type-post { width: 100%; margin-right: 0; }
	.post_share_bubble { left: 30px; }
	.photo_wall_wrapper.shortcode, #photo_wall_wrapper .wall_entry, .photo_wall_wrapper .wall_entry.withmargin { width: 100%; margin: 0; }
	.one_third_bg.team_photo { padding-bottom: 0!important; }
	.map_shortcode_wrapper { max-height: 300px; }
	.testimonial_slider_wrapper { font-size: 16px; }
	#page_content_wrapper .inner .sidebar_content.full_width img, .page_content_wrapper img{ max-width: 100%; height: auto; }
	#footer_menu { float: left; width: 100%; padding: 0 30px 0 30px; box-sizing: border-box; }
	#footer_menu li:first-child { margin-left: 0; }  
  #page_content_wrapper .inner .sidebar_content.full_width#blog_grid_wrapper, .page_content_wrapper .inner .sidebar_content.full_width.blog_grid_wrapper { width: 100%; }
  #page_content_wrapper .inner #blog_grid_wrapper.sidebar_content.left_sidebar { padding-left: 0; }

  	#footer_before_widget_text { width: 85%; }
  	#toTop { right: 10px; }

  	#page_caption.hasbg .post_detail, #page_caption.hasbg .post_detail a, #page_caption.hasbg .post_detail a:hover, #page_caption.hasbg .post_detail a:active, .page_tagline { font-size: 11px; }
  	.post_type_icon i { line-height: 38px; }

  	#searchform input[type=text] { width: 71%; }
  	#page_content_wrapper .inner .sidebar_content #commentform p.comment-form-author, #page_content_wrapper .inner .sidebar_content #commentform p.comment-form-email, #page_content_wrapper .inner .sidebar_content #commentform p.comment-form-url { width: 100%; box-sizing: border-box; }
  	
  .woocommerce ul.products li.product, .woocommerce-page ul.products li.product { width: 100%; margin: 0 0 30px 0; }
  .woocommerce .related ul.products li.product, .woocommerce .related ul li.product, .woocommerce .upsells.products ul.products li.product, .woocommerce .upsells.products ul li.product, .woocommerce-page .related ul.products li.product, .woocommerce-page .related ul li.product, .woocommerce-page .upsells.products ul.products li.product, .woocommerce-page .upsells.products ul li.product { width: 100% !important; margin: 0 0 30px 0; }
  .woocommerce table.cart td.actions .coupon .input-text#coupon_code { width: 48%; }
  
  #logo_wrapper .social_wrapper { display: none; }
  #logo_wrapper, html[data-style=fullscreen] #logo_wrapper, .top_bar.hasbg #logo_wrapper { border: 0; text-align: left; padding: 15px 0 15px 0; }
	
	.three_cols.gallery .element .portfolio_title .image_caption, 
	.two_cols.gallery .element .portfolio_title .image_caption, 
	.four_cols.gallery .element .portfolio_title .image_caption, 
	.five_cols.gallery .element .portfolio_title .image_caption
	{
		 transform: translateY(0px);
		 opacity: 1;
	}
	
	.two_cols.gallery.wide .element, .three_cols.gallery.wide .element, .two_cols.gallery .element, .four_cols.gallery.wide .element, .five_cols.gallery.wide .element
	{
		width: 100%;
		margin-right: 0;
	}
	
	.two_cols.gallery.wide .element, .three_cols.gallery.wide .element, .four_cols.gallery.wide .element, .five_cols.gallery.wide .element
	{
		margin-bottom: 0;
	}
	
	.footer_photostream li
	{
		width: 25%;
	}
	
	.post_excerpt
	{
		width: 100%;
	}
	
	.top_bar.scroll #logo_wrapper
	{
		display: block !important;
	}
	
	.portfolio_desc
	{
		margin-bottom: 15px;
	}
	
	.woocommerce #content .quantity input.qty, .woocommerce .quantity input.qty, .woocommerce-page #content .quantity input.qty, .woocommerce-page .quantity input.qty
	{
		-webkit-appearance: none;
	}
	
	.page_tagline, .post_header .post_detail, .recent_post_detail, .post_detail, .thumb_content span, .portfolio_desc .portfolio_excerpt, .testimonial_customer_position, .testimonial_customer_company
	{
		font-size: 11px;
	}
	
	.five_cols.gallery .element .portfolio_title h5
	{
		font-size: 16px;
	}
	
	.textwidget img
	{
		text-align: center;
	}
	
	.mobile_menu_wrapper
	{
		width: 270px;
		padding: 30px;
	}
	
	.mobile_main_nav li.menu-item-has-children > a:after
	{
		left: 240px;
	}
	
	.fullscreen_share_content #social_share_wrapper ul li a i
	{
		font-size: 3em;
	}
	
	.fullscreen_share_content #social_share_wrapper ul li
	{
		margin: 0 10px 0 10px;
	}
	
	body.js_nav #overlay_background 
	{
	  visibility: visible;
	  opacity: 1;
	}
	
	#mobile_nav_icon
	{
		display: inline-block !important;
	}
	
	.standard_wrapper
	{
		width: 100%;
		padding: 0 20px 0 20px;
		float: left;
	}
	
	.standard_wrapper:empty
	{
		padding: 0;
	}
	
	.header_style_wrapper .standard_wrapper
	{
		padding: 0 10px 0 10px;
	}
	
	.five_cols.gallery .element
	{
		width: 100%;
	}
	
	.one
	{
		width: 100% !important;
		overflow: hidden;
	}
	
	.woocommerce .woocommerce-ordering, .woocommerce-page .woocommerce-ordering
	{
		float: left;
	}
	
	.post_caption
	{
		margin-bottom: 20px;
	}
	
	#option_btn, #option_wrapper
	{
		display: none;
	}
	
	#logo_normal.logo_container, #logo_transparent.logo_container
	{
		left: 0;
		top: 10px;
	}
	
	#menu_wrapper .nav ul li a, #menu_wrapper div .nav li > a, html[data-menu=centeralogo] #logo_right_button
	{
		padding-top: 3px !important;
	}
	
	#mobile_nav_icon
	{
		border-top-width: 2px;
	}
	
	.parallax
	{
		z-index: 0;
		clear: both;
	}
	
	.parallax_scroll
	{
		position: relative !important;
	}
	
	#wrapper
	{
		padding-top: 69px;
	}
	
	.menu-footer-menu-container
	{
		float: left;
	}
	
	.header_style_wrapper.nofixed
	{
		display: block;
	}
	
	.portfolio_mixed_filter_wrapper.contained, .portfolio_mixed_filter_wrapper.three_cols.gallery .element.double_size, .portfolio_mixed_filter_wrapper.three_cols.gallery .element
	{
		width: 100%;
		box-sizing: border-box;
	}
	
	.social_share_button_wrapper .social_post_view, .social_share_button_wrapper .social_post_share_count
	{
		margin: 0 10px 0 10px;
	}
	
	.social_share_button_wrapper ul
	{
		padding-left: 0;
		border: 0;
	}
	
	#page_caption.hasbg .page_title_wrapper .page_title_inner
	{
		box-shadow: 0 0 0;
	}
	
	.floatright
	{
		float: left;
	}
		
	#page_content_wrapper .inner .sidebar_content ul.products
	{
		padding: 20px;
	}
	
	body.single-product #page_content_wrapper .inner .sidebar_content.full_width
	{
		padding-top: 0;
	}
	
	#about_the_author .author_detail
	{
		width: 100%;
		text-align: center;
	}
	
	#about_the_author .gravatar
	{
		margin: auto;
		float: none;
		margin-bottom: 20px;
	}
	
	.author_content .author_label
	{
		float: none;
	}
	
	body.single-post #page_content_wrapper, body.single-attachment #page_content_wrapper
	{
		margin-top: 30px;
		clear: both;
	}
	
	.woocommerce ul.products li.product, .woocommerce-page ul.products li.product
	{
		clear: both;
	}
	
	#page_caption.hasbg
	{
		max-height: 450px;
	}
	
	.grid.gallery .element .grid.gallery2,
	.grid.gallery .element .grid.gallery3,
	.grid.gallery .element .grid.gallery4,
	.sidebar_content:not(.full_width) .grid.gallery .element .grid.gallery2,
	.sidebar_content:not(.full_width) .grid.gallery .element .grid.gallery3,
	.grid.gallery.portrait .element .grid.gallery4
	{
		min-height: 300px;
	}
	
	body.single-post #page_caption .page_title_wrapper
	{
		width: 100%;
	}
	
	html[data-menu=leftalign] #logo_right_button
	{
		top: 0;
	}
	
	#rsvp.button.ghost, .woocommerce #rsvp.button.ghost
	{
		margin-right: 10px;
	}
	
	#page_content_wrapper p.woocommerce-result-count
	{
		padding-bottom: 0;
	}
	
	.woocommerce .woocommerce-ordering
	{
		margin-bottom: 30px;
	}
	
	.woocommerce .star-rating
	{
		float: none;
		clear: both;
		margin-bottom: 5px;
	}
	
	.woocommerce #review_form #respond p.stars
	{
		margin-top: -7px;
	}
	
	body.admin-bar #close_mobile_menu
	{
		top: 46px;
	}
	
	body.admin-bar .header_style_wrapper
	{
		padding-top: 46px;
	}
	
	body.admin-bar .header_style_wrapper.scroll
	{
		padding-top: 0px;
	}
	
	div.wpcf7 .ajax-loader
	{
		display: block;
	}
	
	.page_title_wrapper .standard_wrapper
	{
		padding: 0;
	}
	
	#page_caption.hasbg .page_title_wrapper .page_title_inner
	{
		width: 100%;
	}
	
	#page_caption.hasbg .page_title_wrapper .standard_wrapper
	{
		padding: 0 30px 0 30px;
	}
	
	#blog_grid_wrapper.sidebar_content:not(.full_width) .post.type-post, .post.type-post.grid_layout
	{
		width: 100%;
		margin-top: 20px;
	}
	
	body.admin-bar .frame_top
	{
		top: 46px;
	}
	
	.woocommerce div.product span.price, .woocommerce-page div.product span.price, .woocommerce #content div.product span.price, .woocommerce-page #content div.product span.price, .woocommerce div.product p.price, .woocommerce-page div.product p.price, .woocommerce #content div.product p.price, .woocommerce-page #content div.product p.price
	{
		font-size: 1.3em !important;
	}
	
	#page_content_wrapper .inner .sidebar_content.full_width.blog_f
	{
		width: 100%;
	}
	
	body.js_nav #wrapper
	{
		left: -30px;
	}
	
	body.js_nav #wrapper
	{
		transform: translateZ(0px) translateX(300px) rotateY(0deg);
	}
	
	#sub_menu
	{
		margin: 0;
	}
	
	.woocommerce ul.products li.product, .woocommerce-page ul.products li.product, .woocommerce ul.products.columns-3 li.product, .woocommerce-page ul.products.columns-3 li.product
	{
		width: 100%;
		margin-right: 0;
	}
	
	.header_client_wrapper
	{
		display: none;
	}
	
	#page_caption.hasbg
	{
		z-index: 3 !important;
		margin: 0;
	}
	
	.post_navigation.previous .navigation_anchor
	{
		margin-left: -53px;
	}
	
	.post_navigation.next .navigation_anchor
	{
		margin-right: -35px;
	}
	
	body.centeralign .logo_container .logo_wrapper:not(.hidden)
	{
		margin-top: 0;
	}
	
	body.leftmenu .mobile_menu_wrapper
	{
		left: -10px;
	    -webkit-transition: -webkit-transform 200ms ease;
	    -moz-transition: transform 200ms ease;
	    -o-transition: -o-transform 200ms ease;
	    transition: transform 200ms ease;
	    -webkit-transform: translate(-400px, 0px);
	    -moz-transform: translate(-400px, 0px);
	    transform: translate(-400px, 0px);
	}
	
	body.leftmenu #wrapper
	{
		width: 100%;
		transform-origin: 50% 50% 50%;
		left: 0;
		padding-top: initial !important;
	}
	
	body.leftmenu .tg_fullscreen_gallery_wrapper,
	body.leftmenu .tg_parallax_slide_container,
	body.leftmenu .tg_parallax_slide_background,
	body.leftmenu .tg_animated_frame_slider_wrapper.slideshow,
	body.leftmenu .tg_room_slider_wrapper,
	body.leftmenu .tg_popout_slide_container.slider,
	body.leftmenu .tg_transitions_slide_container
	{
		width: 100%;
		left: 0;
	}
	
	body.leftmenu .icon-scroll {
		left: 50%;
	}
	
	body.leftmenu .tg_room_slider_wrapper .slide
	{
		width: 50%;
		margin: 7vh 0 0 20vw;
	}
	
	body.leftmenu .tg_skewed_slide_container .slider-page__content
	{
		 padding: 0 32% 0 32%;
	}
	
	body.leftmenu .tg_clip_path_slide_container .slide__content
	{
		left: calc(5%);
		bottom: 30px;
	}
	
	body.leftmenu #page_content_wrapper .inner .sidebar_content .tg_split_slick_slide_container .slideshow .slider .item img
	{
		left: 50%;
		min-width: 101%;
	}
	
	body.leftmenu .tg_split_slick_slide_container .slideshow-text {
		left: 50%;
	}
	
	body.leftmenu .logo_container
	{
		display: none;
	}
	
	body.leftmenu .header_style_wrapper
	{
		display: block;
	}
	
	body.leftmenu #footer_wrapper
	{
		width: 100%;
		margin-left: 0;
	}
	
	body.leftmenu .logo_container
	{
		display: block;
	}
	
	body.tg_footer_reveal #footer_wrapper
	{
		position: relative;
	}
	
	body.single-post #page_content_wrapper .inner .sidebar_content,
	body.single-post #page_content_wrapper .inner .sidebar_wrapper .sidebar
	{
		box-sizing: border-box;
	}
	
	.post_navigation.previous .navigation_anchor
	{
		margin-left: -42px;
	}
	
	.post_navigation.next .navigation_anchor
	{
		margin-right: -28px;
	}
	
	#wrapper
	{
		transition: transform 0.2s;
	}
	
	#page_caption.hasbg .page_title_wrapper .page_title_inner .page_title_content
	{
		width: 100%;
	}
	
	body.single-post #page_content_wrapper .inner .sidebar_content,
	body.home.blog #page_content_wrapper .inner .sidebar_content
	{
		padding: 0 20px 0 20px;
		box-sizing: border-box;
	}
	
	body.tg_footer_reveal #wrapper
	{
		margin-bottom: 0 !important;
	}
	
	body.home.blog .type-post:last-child {
		margin-bottom: 0 !important;
	}
	
	 .woocommerce div.product div.summary {
		 margin-top: 20px;
	 }
	 
	 .woocommerce div.product div.images {
		 margin-bottom: 20px;
		 margin-top: 0;
	 }
	 
	 body.single #page_content_wrapper .inner .sidebar_wrapper .sidebar {
		 margin-bottom: 0;
	 }
	 
	 body.error404 #page_content_wrapper:not(.wide), body.error404 .page_content_wrapper:not(.wide) 
	 {
		padding: 0 20px 0 20px !important;
	 }
	 
	 #learn-press-course
	{
		width: 100%;
		clear: both;
		padding: 0 20px 0 20px;
		box-sizing: border-box;
	}
	
	.single_course_title,
	.single_course_join
	{
		width: 100%;
		float: none;
		text-align: left;
	}
	
	#single_course_header
	{
		padding: 40px 0 60px 0;
	}
	
	#single_course_meta
	{
		margin: 60px 0 0 0;
	}
	
	#single_course_meta ul.single_course_meta_data
	{
		display: block;
		padding: 20px;
	}
	
	#single_course_meta ul.single_course_meta_data li
	{
		margin: 1.3611rem 0;
		display: flex;
		padding: 0;
		justify-content: left;
	}
	
	#single_course_meta ul.single_course_meta_data li.single_course_meta_data_separator
	{
		display: none;
	}
	
	#single_course_meta ul.single_course_meta_data li .single_course_meta_data_icon
	{
		font-size: 1.5rem;
	}
	
	#single_course_meta ul.single_course_meta_data li .single_course_meta_data_text
	{
		display: block;
	}
	
	.single_course_title h1
	{
		font-size: 28px;
	}
	
	body .course-curriculum ul.curriculum-sections .section-content .course-item .course-item-meta
	{
		display: none;
	}
	
	body #course-item-content-header .form-button.lp-button-back button, body.learnpress-page .lp-button
	{
		padding: 6px 20px 4px 20px;
	}
	
	body #course-item-content-header .form-button.lp-button-back
	{
		position: fixed;
		bottom: 10px;
		right: 0px;
	}
	
	body #course-item-content-header .course-title
	{
		display: none;
	}
	
	body.learnpress-page.checkout .learn-press-checkout-comment,
	body.learnpress-page.checkout  #learn-press-payment,
	body.learnpress-page.checkout #learn-press-order-review
	{
		width: 100%;
		float: none;
		clear: both;
	}
	
	body.learnpress-page.checkout #learn-press-order-review
	{
		margin-bottom: 20px;
	}
	
	body #page_content_wrapper:not(.wide), body .page_content_wrapper:not(.wide)
	{
		padding: 0 20px 0 20px;
	}
	
	body #learn-press-profile-nav .learn-press-tabs,
	body #learn-press-profile-nav .tabs > li:hover a
	{
		background: #f9f9f9;
	}
	
	body #learn-press-profile-nav .learn-press-tabs li a
	{
		color: #222;
	}
	
	body #learn-press-profile-nav .learn-press-tabs li a:hover
	{
		color: #fff;
	}
	
	body.learnpress-page.profile #learn-press-profile-content
	{
		float: none;
		width: 100%;
	}
	
	body.learnpress-page.profile #learn-press-profile-header
	{
		float: left;
		width: 50%;
	}
	
	#learn-press-profile-header .lp-profile-avatar
	{
		left: 0;
		z-index: 0;
	}
	
	body.learnpress-page.profile #learn-press-profile-nav:before
	{
		background: transparent;
	}
	
	#learn-press-profile-nav:hover .learn-press-tabs
	{
		z-index: 9;
	}
	
	body ul.learn-press-courses .course .course-thumbnail img
	{
		width: 100%;
	}
	
	body.learnpress-page.profile #learn-press-profile-nav .tabs > li.active ul li a,
	body.learnpress-page.profile #learn-press-profile-nav .tabs > li.active ul li a:hover
	{
		background: #f9f9f9;
		color: #222;
	}
	
	#page_content_wrapper ul.learn-press-nav-tabs .course-nav a, body.learnpress-page.profile .lp-tab-sections li a, body.learnpress-page.profile .lp-tab-sections li span, body.learnpress-page.profile .lp-tab-sections .section-tab.active span
	{
		padding: 5px 20px 5px 15px;
	}
	
	body #learn-press-profile-header .lp-profile-avatar
	{
		left: 0;
	}
	
	body.woocommerce-checkout .woocommerce #order_review, 
	 body.woocommerce-checkout .woocommerce .col2-set, 
	 body.woocommerce-checkout.woocommerce-page .col2-set,
	 body.woocommerce-cart .woocommerce,
	 body.woocommerce-checkout .woocommerce
	 {
		width: 100%;
		float: none;
		padding: 0 20px 0 20px;
		box-sizing: border-box;
	}
	
	body.woocommerce-checkout .woocommerce #order_review, 
	body.woocommerce-checkout .woocommerce .col2-set, 
	body.woocommerce-checkout.woocommerce-page .col2-set,
	.woocommerce-checkout #payment div.form-row
	{
		padding: 0;
	}
	
	.woocommerce table.shop_table
	{
		margin-bottom: 40px;
	}
	
	body.single-post #page_content_wrapper.blog_wrapper
	{
		padding: 40px;
	}
	
	body.single-post #page_caption:not(.hasbg)
	{
		padding-bottom: 40px;
	}
	
	body ul.learn-press-courses .course
	{
		width: 100%;
		margin-right: 0;
	}
	
	body.single.single-post #page_content_wrapper.blog_wrapper .page_title_content h1
	{
		font-size: 40px;
		width: 100%;
	}
	
	body.single-post #page_content_wrapper.blog_wrapper .page_title_content
	{
		margin-bottom: 20px;
		padding-bottom: 40px;
	}
	
	body.single.single-lp_course #lp-single-course .single_course_title h1, 
	body.single.single-meeting .single_course_title h1
	{
		font-size: 36px;
		line-height: 1.3;
	}
}

/* 
#Mobile (Landscape)
================================================== 
*/

@media only screen and (min-width: 480px) and (max-width: 767px) {
	body { -webkit-text-size-adjust: none; }
	.map_shortcode_wrapper { max-height: 200px; }
	#page_content_wrapper .inner .sidebar_content #commentform p.comment-form-author, #page_content_wrapper .inner .sidebar_content #commentform p.comment-form-email, #page_content_wrapper .inner .sidebar_content #commentform p.comment-form-url { width: 25.8%; }
	
	#page_caption.hasbg .page_title_wrapper .page_title_inner
	{
		padding: 25px;
		bottom: 10px;
	}
}

@media screen and (max-width: 667px) and (orientation:landscape) {
  body { -webkit-text-size-adjust: none; }
}