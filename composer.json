{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2.0", "algolia/algoliasearch-client-php": "^3.3", "artesaos/seotools": "^1.1", "doctrine/dbal": "^3.5", "guzzlehttp/guzzle": "^7.2", "inertiajs/inertia-laravel": "^1.0", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/scout": "^10.9", "laravel/tinker": "^2.7", "maatwebsite/excel": "^3.1", "posthog/posthog-php": "^3.3", "psr/simple-cache": "2.0", "spatie/laravel-cookie-consent": "^3.2", "spatie/laravel-medialibrary": "^11.5", "spatie/laravel-sitemap": "^7.3", "spatie/laravel-sluggable": "^3.6", "spatie/laravel-tags": "^4.6", "tightenco/ziggy": "^1.0", "webpatser/laravel-countries": "^1.5"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/breeze": "^2.0", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.1.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "phpstan/extension-installer": true}}, "minimum-stability": "dev", "prefer-stable": true}