import React from 'react';
import {Head, router} from "@inertiajs/react";
import {useFormik} from "formik";
import * as Yup from "yup";
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import {useDropzone} from 'react-dropzone';
import axios from "axios";
import PrimaryButton from "@/Components/PrimaryButton";
import AdminLayout from "@/Layouts/AdminLayout";
import {Textarea} from "@/Components/ui/textarea";
import {LocationType} from "@/types/location";
import {Input} from "@/Components/ui/input";
import {Label} from "@/Components/ui/label";
import {ProfileType} from "@/types/admin-employees";

function Create( {employee} : { employee: ProfileType }) {


  const [ imagePreview, setImagePreview ] = React.useState( employee?.image ? employee.image : null );


  const formik = useFormik( {
    initialValues: {
      name: employee?.name ? employee.name : '',
      address: employee?.about ? employee.about : '',
      email: employee?.email ? employee.email : '',
      dob: employee?.dob ? employee.dob : '',
      phone: employee?.phone ? employee.phone : '',
      linkedin: employee?.linkedin ? employee.linkedin : '',
      website: employee?.website ? employee.website : '',
      fee: employee?.fee ? employee.fee : '',
      position: employee?.position ? employee.position : '',
      image: null,
    },

    validationSchema: Yup.object( {
      name: Yup.string()
        .required( 'First Name is required' ),
      email: Yup.string(),
      dob: Yup.date(),
      phone: Yup.string(),
      about: Yup.string()
        .required( 'About is required' ),
    } ),

    onSubmit: values => {
      let url = route( 'admin.employees.store' );
      let method = 'post';

      if ( employee ) {
        url = route( 'admin.employees.update', employee.id );
        method = 'put';
      }

      axios( {
        method: method,
        url: url,
        data: values,
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      } ).then( ( response ) => {
        formik.setSubmitting( false );
        router.visit( route( 'admin.employees.index' ) );
      } ).catch( ( error ) => {
        //Set formik errors
        if ( error.response.status === 422 ) {
          formik.setErrors( error.response.data.errors );
        }
        formik.setSubmitting( false );
      } );
    },
  } );


  const {
    acceptedFiles,
    getRootProps: getRootProps,
    getInputProps: getInputProps,
  } = useDropzone( {
    maxFiles: 1,
    accept: {
      'image/*': [],
    },
    onDrop: ( acceptedFiles ) => {
      setImagePreview( URL.createObjectURL( acceptedFiles[ 0 ] ) );
      formik.setFieldValue( 'image', acceptedFiles[ 0 ] );
    },
  } );


  return (
    <AdminLayout
    >
      <Head title="Add Profile"/>

      <div className="">
        <div className="sm:px-6 lg:px-8">
          <div className="">
            <h2 className=" font-semibold text-lg text-gray-900">Add Speaker Profile</h2>
            <p className={'text-neutral-500'}>This is private employee for internal use</p>
          </div>
        </div>
        <div className="sm:p-6 lg:p-4 bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <form onSubmit={formik.handleSubmit}
                className=" max-w-4xl space-y-8 mx-auto py-8 px-8">
            <div className="grid grid-cols-2 gap-6">
              <div>
                <Label htmlFor="name" className="block text-sm font-medium text-gray-700">First Name</Label>
                <div className="mt-1">
                  <Input type="text"
                         name="first_name"
                         id="first_name"
                         value={formik.values.first_name}
                         onChange={formik.handleChange}
                         className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                         placeholder="First Name"/>
                </div>

                {
                  formik.touched.first_name && formik.errors.first_name ? (
                    <div className="text-red-500 text-xs italic">{formik.errors.first_name}</div>
                  ) : null
                }
              </div>
              <div>
                <Label htmlFor="name" className="block text-sm font-medium text-gray-700">Last Name</Label>
                <div className="mt-1">
                  <Input type="text"
                         name="last_name"
                         id="last_name"
                         value={formik.values.last_name}
                         onChange={formik.handleChange}
                         className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                         placeholder="Last Name"/>
                </div>

                {
                  formik.touched.last_name && formik.errors.last_name ? (
                    <div className="text-red-500 text-xs italic">{formik.errors.last_name}</div>
                  ) : null
                }
              </div>
            </div>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <Label htmlFor="name" className="block text-sm font-medium text-gray-700">Email</Label>
                <div className="mt-1">
                  <Input type="email"
                         name="email"
                         id="email"
                         value={formik.values.email}
                         onChange={formik.handleChange}
                         className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                         placeholder="Email"/>
                </div>

                {
                  formik.touched.email && formik.errors.email ? (
                    <div className="text-red-500 text-xs italic">{formik.errors.email}</div>
                  ) : null
                }
              </div>
              <div>
                <Label htmlFor="name" className="block text-sm font-medium text-gray-700">Phone</Label>
                <div className="mt-1">
                  <Input type="text"
                         name="phone"
                         id="phone"
                         value={formik.values.phone}
                         onChange={formik.handleChange}
                         className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                         placeholder="Phone"/>
                </div>

                {
                  formik.touched.phone && formik.errors.phone ? (
                    <div className="text-red-500 text-xs italic">{formik.errors.phone}</div>
                  ) : null
                }
              </div>
            </div>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <Label htmlFor="name" className="block text-sm font-medium text-gray-700">Linkedin</Label>
                <div className="mt-1">
                  <Input type="text"
                         name="linkedin"
                         id="linkedin"
                         value={formik.values.linkedin}
                         onChange={formik.handleChange}
                         className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                         placeholder="LinkedIn"/>
                </div>

                {
                  formik.touched.linkedin && formik.errors.linkedin ? (
                    <div className="text-red-500 text-xs italic">{formik.errors.linkedin}</div>
                  ) : null
                }
              </div>
              <div>
                <Label htmlFor="name" className="block text-sm font-medium text-gray-700">Website</Label>
                <div className="mt-1">
                  <Input type="text"
                         name="website"
                         id="website"
                         value={formik.values.website}
                         onChange={formik.handleChange}
                         className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                         placeholder="website"/>
                </div>

                {
                  formik.touched.website && formik.errors.website ? (
                    <div className="text-red-500 text-xs italic">{formik.errors.website}</div>
                  ) : null
                }
              </div>

            </div>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <Label htmlFor="fee" className="block text-sm font-medium text-gray-700">Fee</Label>
                <div className="mt-1">
                  <Input type="text"
                         name="fee"
                         id="fee"
                         value={formik.values.fee}
                         onChange={formik.handleChange}
                         className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                         placeholder="Default fee"/>
                </div>

                {
                  formik.touched.fee && formik.errors.fee ? (
                    <div className="text-red-500 text-xs italic">{formik.errors.fee}</div>
                  ) : null
                }
              </div>
              <div>
                <Label htmlFor="name" className="block text-sm font-medium text-gray-700">Job Title</Label>
                <div className="mt-1">
                  <Input type="text"
                         name="job_title"
                         id="job_title"
                         value={formik.values.job_title}
                         onChange={formik.handleChange}
                         className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                         placeholder="Job Title"/>
                </div>

                {
                  formik.touched.job_title && formik.errors.job_title ? (
                    <div className="text-red-500 text-xs italic">{formik.errors.job_title}</div>
                  ) : null
                }
              </div>
            </div>
             <div className="grid grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="dob" className="block text-sm font-medium text-gray-700">Date of Birth</Label>
                    <div className="mt-1">
                      <Input type="date"
                             name="dob"
                             id="dob"
                             value={formik.values.dob}
                             onChange={formik.handleChange}
                             className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                             placeholder="Job Title"/>
                    </div>

                    {
                      formik.touched.dob && formik.errors.dob ? (
                        <div className="text-red-500 text-xs italic">{formik.errors.dob}</div>
                      ) : null
                    }
                  </div>
                </div>

            <div>
              <Label htmlFor="bio" className="block text-sm font-medium text-gray-700">About</Label>

              <div className="mt-1">
                <ReactQuill
                  theme='snow'
                  style={{height: '200px'}}
                  value={formik.values.about}
                  onChange={( e ) => formik.setFieldValue( 'about', e )}
                />

              </div>

              {formik.touched.about && formik.errors.about ? (
                <div className='m-0.5 text-sm text-red-500'>{formik.errors.about}</div>
              ) : null}

            </div>

            <div>
              <div className='w-full lg:w-1/2 pt-6 '>
                <Label htmlFor={'file'} >Avatar</Label>
                <div {...getRootProps( {className: 'border-dashed border-2 rounded-lg mt-2 py-4 px-4'} )}>
                  <Input {...getInputProps()} />
                  <p className={'text-sm'}>Drag 'n' Avatar Image, or click to select files</p>
                </div>

                {/*    display preview */}
                {imagePreview && (
                  <div className='mx-auto mt-3'>
                    <img src={imagePreview} className={'h-40 w-40 object-cover'} alt='' />
                  </div>
                )}

              </div>


            </div>

            <div>
              <div className="mt-1 flex justify-end">
                <PrimaryButton disabled={formik.isSubmitting} type="submit">
                  {
                    employee ? 'Update Profile' : 'Add Profile'
                  }
                </PrimaryButton>
              </div>
            </div>
          </form>
        </div>
      </div>
    </AdminLayout>
  );
}

export default Create;
